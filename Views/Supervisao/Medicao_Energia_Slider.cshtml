﻿@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicao;

    int IDConsultor = ViewBag._IDConsultor;
    int IDCliente = ViewBag._IDCliente;
}

<style>

    .centered {
        margin: 0 auto !important;
        float: none !important;
    }
</style>

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="row">
        <div class="col-lg-12">
            <div class="superv-content">

                <div class="col-lg-3 col-md-12">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.DemandaMaximaDia</h5>
                                    <h1>@ViewBag.Dem_Dia_DemMaxP <span>@ViewBag.UnidadeDemanda</span></h1>
                                    <h6>@ViewBag.Dem_Dia_DemMaxP_DataHora</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-fponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.DemandaMaximaDia</h5>
                                    <h1>@ViewBag.Dem_Dia_DemMaxFP <span>@ViewBag.UnidadeDemanda</span></h1>
                                    <h6>@ViewBag.Dem_Dia_DemMaxFP_DataHora</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela">
                                <table class="table no-margins" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th style="width:34%;"></th>
                                            <th class="superv-ponta" style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                            <th class="superv-fponta" style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAtual<br /><small>(@ViewBag.MesAtual)</small></td>
                                            <td class="superv-ponta">@ViewBag.Dem_Mes_DemMaxP @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_Mes_DemMaxP_DataHora)</small></td>
                                            <td class="superv-fponta">@ViewBag.Dem_Mes_DemMaxFP @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_Mes_DemMaxFP_DataHora)</small></td>
                                        </tr>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAnterior<br /><small>(@ViewBag.MesAnterior1)</small></td>
                                            <td class="superv-ponta">@ViewBag.Dem_MesAnt_DemMaxP @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_MesAnt_DemMaxP_DataHora)</small></td>
                                            <td class="superv-fponta">@ViewBag.Dem_MesAnt_DemMaxFP @ViewBag.UnidadeDemanda<br /><small>(@ViewBag.Dem_MesAnt_DemMaxFP_DataHora)</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual">

                                    @{
                                        if (ViewBag.Dem_Mes_UltrapassagemP == 0 && ViewBag.Dem_Mes_UltrapassagemFP == 0)
                                        {
                                            <!-- <h5 class="superv-fponta"><i class="fa fa-check-square-o" style="font-size:20px;"></i><span style="font-weight: bold; padding-left:8px;">Valores de Demanda abaixo do valor de Contrato.</span></h5> -->
                                        }
                                        else
                                        {
                                            if (ViewBag.Dem_Mes_UltrapassagemP > 0)
                                            {
                                                <h5 class="superv-ponta"><blink><i class="fa fa-line-chart" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">@SmartEnergy.Resources.SupervisaoTexts.UltrapassagemDemanda @SmartEnergy.Resources.SupervisaoTexts.Ponta</span></h5>
                                            }

                                            if (ViewBag.Dem_Mes_UltrapassagemFP > 0)
                                            {
                                                <h5 class="superv-ponta" style="padding-top:10px;"><blink><i class="fa fa-line-chart" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">@SmartEnergy.Resources.SupervisaoTexts.UltrapassagemDemanda @SmartEnergy.Resources.SupervisaoTexts.ForaPonta</span></h5>
                                            }
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-5 col-md-12">
                    <div class="row">
                        <div class="superv-grafico">
                            <div class="flot-chart">
                                <div id="demanda-chart"></div>
                            </div>
                            <div class='chart-legend'>
                                <div class='legend-scale-dem'>
                                    <ul class='legend-labels'>
                                        <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                        <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.ForaPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                        <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-8">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Ponta</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoTotalDia</h5>
                                    <h1>@ViewBag.Cons_Dia_ConsumoP <span>@ViewBag.UnidadeConsumo</span></h1>
                                    <h6>@ViewBag.DiaAtual</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-fponta">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.ConsumoTotalDia</h5>
                                    <h1>@ViewBag.Cons_Dia_ConsumoFP <span>@ViewBag.UnidadeConsumo</span></h1>
                                    <h6>@ViewBag.DiaAtual</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela superv-energia">
                                <table class="table no-margins">
                                    <thead>
                                        <tr>
                                            <th style="width: 25%;"></th>
                                            <th class="superv-ponta" style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.Ponta</th>
                                            <th class="superv-fponta" style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.ForaPonta</th>
                                            <th style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAtual @SmartEnergy.Resources.SupervisaoTexts.Projetado<br /><small>(@ViewBag.MesAtual)</small></td>
                                            <td class="superv-ponta">@ViewBag.Cons_MesProj_ConsumoP @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                            <td class="superv-fponta">@ViewBag.Cons_MesProj_ConsumoFP @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Cons_MesProj_ConsumoTotal @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                        </tr>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAnterior<br /><small>(@ViewBag.MesAnterior1)</small></td>
                                            <td class="superv-ponta">@ViewBag.Cons_MesAnt_ConsumoP @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                            <td class="superv-fponta">@ViewBag.Cons_MesAnt_ConsumoFP @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Cons_MesAnt_ConsumoTotal @ViewBag.UnidadeConsumo<br /><small>&nbsp;</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual">

                                    @{
                                        if (ViewBag.Cons_MesProj_PorcAntN > 0)
                                        {
                                            <h5 class="superv-ponta"><blink><i class="fa fa-arrow-circle-up" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">@SmartEnergy.Resources.SupervisaoTexts.ConsumoAumentou @ViewBag.Cons_MesProj_PorcAnt</span></h5>
                                        }

                                        if (ViewBag.Cons_MesProj_PorcAntN < 0)
                                        {
                                            <h5 class="superv-fponta"><blink><i class="fa fa-arrow-circle-down" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">@SmartEnergy.Resources.SupervisaoTexts.ConsumoReduziu @ViewBag.Cons_MesProj_PorcAnt</span></h5>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<div class="wrapper wrapper-content">

    <div class="row centered">
        <div class="col-lg-12">
            <div class="slick_superv">

                <div>
                    <div class="panel panel-title">
                        <div class="panel-heading">
                            @SmartEnergy.Resources.ConfiguracaoTexts.FatPot
                        </div>
                        <div class="panel-body" style="height:360px;">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="col-lg-6 col-md-12">
                                        <div class="row">
                                            <div class="superv-grafico">
                                                <div class="flot-chart" style="height:280px;">
                                                    <div id="fatpot-chart"></div>
                                                </div>
                                                <div class='chart-legend'>
                                                    <div class='legend-scale-fatpot'>
                                                        <ul class='legend-labels'>
                                                            <li><span style='background:#1C84C6;'></span> @SmartEnergy.Resources.SupervisaoTexts.FPonta (@SmartEnergy.Resources.SupervisaoTexts.Capacitivo)</li>
                                                            <li><span style='background:#007f00;'></span> @SmartEnergy.Resources.SupervisaoTexts.FPonta (@SmartEnergy.Resources.SupervisaoTexts.Indutivo)</li>
                                                            <li><span style='background:#ff0000;'></span>@SmartEnergy.Resources.SupervisaoTexts.Ponta</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="row">
                                            <div class="col-lg-6 col-md-6">
                                                <div class="superv-coluna">
                                                    <div class="superv-valorAtual superv-capacitivo">
                                                        <h4>@SmartEnergy.Resources.SupervisaoTexts.PostoCapacitivo</h4>
                                                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.Maximo</h5>
                                                        <h1>@ViewBag.FatPot_Dia_FatPotFPC</h1>
                                                        <h6>@ViewBag.FatPot_Dia_FatPotFPC_DataHora</h6>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6 col-md-6">
                                                <div class="superv-coluna">
                                                    <div class="superv-valorAtual superv-indutivo">
                                                        <h4>@SmartEnergy.Resources.SupervisaoTexts.PostoIndutivo</h4>
                                                        <h5>@SmartEnergy.Resources.SupervisaoTexts.FatorPotencia @SmartEnergy.Resources.SupervisaoTexts.Maximo</h5>
                                                        <h1>@ViewBag.FatPot_Dia_FatPotFPI</h1>
                                                        <h6>@ViewBag.FatPot_Dia_FatPotFPI_DataHora</h6>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="superv-tabela">
                                                    <table class="table no-margins" style="width:100%;">
                                                        <thead>
                                                            <tr>
                                                                <th style="width:34%;"></th>
                                                                <th class="superv-capacitivo" style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.PostoCapacitivo</th>
                                                                <th class="superv-indutivo" style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.PostoIndutivo</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td>@SmartEnergy.Resources.SupervisaoTexts.MesAtual<br /><small>(@ViewBag.MesAtual)</small></td>
                                                                <td class="superv-capacitivo">@ViewBag.FatPot_Mes_FatPotFPC<br /><small>(@ViewBag.FatPot_Mes_FatPotFPC_DataHora)</small></td>
                                                                <td class="superv-indutivo">@ViewBag.FatPot_Mes_FatPotFPI<br /><small>(@ViewBag.FatPot_Mes_FatPotFPI_DataHora)</small></td>
                                                            </tr>
                                                            <tr>
                                                                <td>@SmartEnergy.Resources.SupervisaoTexts.MesAnterior<br /><small>(@ViewBag.MesAnterior1)</small></td>
                                                                <td class="superv-capacitivo">@ViewBag.FatPot_MesAnt_FatPotFPC<br /><small>(@ViewBag.FatPot_MesAnt_FatPotFPC_DataHora)</small></td>
                                                                <td class="superv-indutivo">@ViewBag.FatPot_MesAnt_FatPotFPI<br /><small>(@ViewBag.FatPot_MesAnt_FatPotFPI_DataHora)</small></td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @{
                    bool View_Financas = (ViewBag.View_Financas == 1) ? true : false;

                    // verifica se usuário pode ver FATURA
                    if (View_Financas)
                    {
                        <div>
                            <div class="panel panel-title">
                                <div class="panel-heading">
                                    @SmartEnergy.Resources.FinancasTexts.FaturaEnergia
                                </div>
                                <div class="panel-body" style="height:360px;">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="col-lg-6 col-md-12">
                                                <div class="row">
                                                    <div class="superv-grafico-fatura">
                                                        <div class="flot-chart">
                                                            <div id="fatura-chart"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-lg-6">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="superv-coluna">
                                                            <div class="superv-valorAtual superv-fponta">
                                                                <h5>@SmartEnergy.Resources.DashboardTexts.FaturaAtual</h5>
                                                                <h1>@ViewBag.Fatura_Atual_ValorTotal</h1>
                                                                <h6>@ViewBag.Fatura_Atual_Data</h6>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="superv-tabela">
                                                            <table class="table no-margins" style="width:100%;">
                                                                <thead>
                                                                    <tr>
                                                                        <th style="width:30%;"></th>
                                                                        <th style="width:5%;"></th>
                                                                        <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                                                                        <th style="width:15%;"></th>
                                                                        <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.CustoMedio</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr>
                                                                        <td>@ViewBag.Fatura_AtualProj_Data<br /><small>Projetada</small></td>
                                                                        <td><i class="fa fa-flag" style="color:@ViewBag.Fatura_AtualProj_Bandeira" data-toggle="tooltip" data-placement="left" title="Bandeira @ViewBag.Fatura_AtualProj_BandeiraTexto"></i></td>
                                                                        <td class="superv-energia">@ViewBag.Fatura_AtualProj_ValorTotal</td>

                                                                        @if (@ViewBag.Fatura_PorcAnt1ProjN == 0.0)
                                                                        {
                                                                            <td style='color:#007f00'></td>
                                                                        }

                                                                        @if (@ViewBag.Fatura_PorcAnt1ProjN > 0.0)
                                                                        {
                                                                            <td style='color:#ff0000'><i class='fa fa-level-up'></i> @ViewBag.Fatura_PorcAnt1Proj</td>
                                                                        }

                                                                        @if (@ViewBag.Fatura_PorcAnt1ProjN < 0.0)
                                                                        {
                                                                            <td style='color:#007f00'><i class='fa fa-level-down'></i> @ViewBag.Fatura_PorcAnt1Proj</td>
                                                                        }

                                                                        <td class="superv-energia">@ViewBag.Fatura_AtualProj_CustoMedio</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>@ViewBag.Fatura_Anterior1_Data</td>
                                                                        <td><i class="fa fa-flag" style="color:@ViewBag.Fatura_Anterior1_Bandeira" data-toggle="tooltip" data-placement="left" title="Bandeira @ViewBag.Fatura_Anterior1_BandeiraTexto"></i></td>
                                                                        <td class="superv-energia">@ViewBag.Fatura_Anterior1_ValorTotal</td>

                                                                        @if (@ViewBag.Fatura_PorcAnt2Ant1N == 0.0)
                                                                        {
                                                                            <td style='color:#ff0000'></td>
                                                                        }

                                                                        @if (@ViewBag.Fatura_PorcAnt2Ant1N > 0.0)
                                                                        {
                                                                            <td style='color:#ff0000'><i class='fa fa-level-up'></i> @ViewBag.Fatura_PorcAnt2Ant1</td>
                                                                        }

                                                                        @if (@ViewBag.Fatura_PorcAnt2Ant1N < 0.0)
                                                                        {
                                                                            <td style='color:#007f00'><i class='fa fa-level-down'></i> @ViewBag.Fatura_PorcAnt2Ant1</td>
                                                                        }

                                                                        <td class="superv-energia">@ViewBag.Fatura_Anterior1_CustoMedio</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>@ViewBag.Fatura_Anterior2_Data</td>
                                                                        <td><i class="fa fa-flag" style="color:@ViewBag.Fatura_Anterior2_Bandeira" data-toggle="tooltip" data-placement="left" title="Bandeira @ViewBag.Fatura_Anterior2_BandeiraTexto"></i></td>
                                                                        <td class="superv-energia">@ViewBag.Fatura_Anterior2_ValorTotal</td>
                                                                        <td></td>
                                                                        <td class="superv-energia">@ViewBag.Fatura_Anterior2_CustoMedio</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                }

                <div>
                    <div class="panel panel-title">
                        <div class="panel-heading">
                            @SmartEnergy.Resources.RelatoriosTexts.Eventos
                        </div>
                        <div class="panel-body" style="height:360px;">

                            <div class="row">
                                <div class="col-lg-12">

                                    @{
                                        List<EventosDescricao> eventos = ViewBag.listaEventosDescricao;

                                        if (eventos != null)
                                        {
                                            if (eventos.Count >= 500)
                                            {
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <div class="widget style1 red-bg">
                                                            <div class="row">
                                                                <div class="col-xs-4">
                                                                    <i class="fa fa-exclamation-triangle fa-5x"></i>
                                                                </div>
                                                                <div class="col-xs-8 text-right">
                                                                    <h3 class="font-bold">@SmartEnergy.Resources.SupervisaoTexts.UltrapassouLimite</h3>
                                                                    <br>
                                                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.NaoApresentaEventos</h4>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <br />
                                            }
                                        }
                                    }

                                    <select class="select2_tipo form-control" id="TiposEvento" onchange="SelecionouTipo()">

                                        @{
                                            var icone_tipo = "fa-th-list";
                                            int tipo = 0;

                                            for (var i = 0; i < ViewBag.NumTiposEventos; i++)
                                            {
                                                tipo = ViewBag.listaTiposEventosDescricao[i].Tipo;

                                                switch (tipo)
                                                {
                                                    case 0:
                                                        icone_tipo = "fa-th-list";
                                                        break;

                                                    case 1:
                                                        icone_tipo = "fa-bell";
                                                        break;

                                                    case 2:
                                                        icone_tipo = "fa-gear";
                                                        break;

                                                    case 3:
                                                        icone_tipo = "fa-power-off";
                                                        break;

                                                    case 4:
                                                        icone_tipo = "fa-retweet";
                                                        break;

                                                    case 5:
                                                        icone_tipo = "fa-sitemap";
                                                        break;

                                                    case 6:
                                                        icone_tipo = "fa-sign-in";
                                                        break;

                                                    case 7:
                                                        icone_tipo = "fa-sign-out";
                                                        break;

                                                    case 8:
                                                        icone_tipo = "fa-upload";
                                                        break;

                                                    case 9:
                                                        icone_tipo = "fa-clock-o";
                                                        break;

                                                    case 10:
                                                        icone_tipo = "fa-user";
                                                        break;
                                                }

                                                <option value=@tipo data-icon=@icone_tipo>&nbsp;&nbsp;@ViewBag.listaTiposEventosDescricao[i].Descricao</option>
                                            }
                                        }

                                    </select>

                                    <br /><br />

                                    <table class="table table-striped table-bordered table-hover dataTables-eventos">
                                        <thead>
                                            <tr>
                                                <th>@SmartEnergy.Resources.RelatoriosTexts.Data</th>
                                                <th>Tipo</th>
                                                <th></th>
                                                <th>@SmartEnergy.Resources.RelatoriosTexts.Evento</th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                            @foreach (var evento in @ViewBag.listaEventosDescricao)
                                            {
                                                icone_tipo = "fa-th-list";
                                                tipo = evento.Tipo;

                                                switch (tipo)
                                                {
                                                    case 0:
                                                        icone_tipo = "fa-th-list";
                                                        break;

                                                    case 1:
                                                        icone_tipo = "fa-bell";
                                                        break;

                                                    case 2:
                                                        icone_tipo = "fa-gear";
                                                        break;

                                                    case 3:
                                                        icone_tipo = "fa-power-off";
                                                        break;

                                                    case 4:
                                                        icone_tipo = "fa-retweet";
                                                        break;

                                                    case 5:
                                                        icone_tipo = "fa-sitemap";
                                                        break;

                                                    case 6:
                                                        icone_tipo = "fa-sign-in";
                                                        break;

                                                    case 7:
                                                        icone_tipo = "fa-sign-out";
                                                        break;

                                                    case 8:
                                                        icone_tipo = "fa-upload";
                                                        break;

                                                    case 9:
                                                        icone_tipo = "fa-clock-o";
                                                        break;

                                                    case 10:
                                                        icone_tipo = "fa-user";
                                                        break;
                                                }

                                                <tr>
                                                    <td style="font-size:11px;"><span style="display:none;">@evento.DataHora_Sort</span>@evento.DataHora</td>
                                                    <td>@evento.Tipo</td>
                                                    <td style="text-align:center;"><i class="fa @icone_tipo" style="font-size:20px;"></i></td>
                                                    <td style="font-size:11px;">@evento.Descricao</td>
                                                </tr>
                                            }

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <br />

    @{
        // RETHA nao quer que aparece GATEWAY
        if (IDConsultor != 4749)
        {

            <div class="row">
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>Gateway</h4><br />

                            @{
                                int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                if (IDTipoAcesso == 0 || IDTipoAcesso == 5)
                                {
                                    <span>[@ViewBag._IDGateway] </span>
                                }
                            }
                            @ViewBag.GatewayNome
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.ModeloVersao</h4><br />
                            @ViewBag.GatewayModelo
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>Status</h4><br />
                            @ViewBag.GatewayStatus
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</h4><br />
                            @ViewBag.GatewayAtualizacao
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.DataHoraEq</h4><br />
                            @ViewBag.GatewayDataEq
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.Constante</h4><br />
                            @ViewBag.Constante
                        </div>
                    </div>
                </div>
            </div>

        }
    }


    @if (@ViewBag.listaErros.Count > 0)
    {
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-danger">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Erros</h4><br />
                        <ul style="list-style-type:disc; margin-left: 20px;">
                            @foreach (var erro in @ViewBag.listaErros)
                            {
                                <li>@erro</li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }

</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/plugins/slickStyles")
    @Styles.Render("~/Supervisao.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/select2")
    @Scripts.Render("~/plugins/slick")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-en.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "es-AR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-es-AR.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-pt-br.js"></script>
    }


    <script type="text/javascript">
    $(document).ready(function () {

        $('.slick_superv').slick({
            dots: true
        });

        $('.slick_demo_2').slick({
            infinite: true,
            slidesToShow: 3,
            slidesToScroll: 1,
            centerMode: true,
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 3,
                        infinite: true,
                        dots: true
                    }
                },
                {
                    breakpoint: 600,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 2
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1
                    }
                }
            ]
        });
        $('.slick_demo_3').slick({
            infinite: true,
            speed: 500,
            fade: true,
            cssEase: 'linear',
            adaptiveHeight: true
        });

        //
        // DEMANDA
        //
        var dataX = [];
        var labelX = [];
        var dataContratoP = [];
        var dataContratoFPI = [];
        var dataContratoFPC = [];
        var dataToleranciaP = [];
        var dataToleranciaFPI = [];
        var dataToleranciaFPC = [];
        var dataContrato = [];
        var dataTolerancia = [];
        var dataDemanda = [];

        // copia valores
        var Demanda = @Html.Raw(Json.Encode(@ViewBag.Demanda));
        var Contrato = @Html.Raw(Json.Encode(@ViewBag.Contrato));
        var Tolerancia = @Html.Raw(Json.Encode(@ViewBag.Tolerancia));
        var Periodo = @Html.Raw(Json.Encode(@ViewBag.Periodo));
        var Dias_Demanda = @Html.Raw(Json.Encode(@ViewBag.Dias_Demanda));

        var maximoDem = @Html.Raw(Json.Encode(@ViewBag.DemMax));
        var UnidadeDemanda = @Html.Raw(Json.Encode(@ViewBag.UnidadeDemanda));

        var IDTipoGrafico_Demanda = @Html.Raw(Json.Encode(@ViewBag._IDTipoGrafico_Demanda));

        dataX.push('x');
        dataDemanda.push(['data1']);
        dataContratoFPC.push(['data2']);
        dataContratoFPI.push(['data3']);
        dataContratoP.push(['data4']);
        dataToleranciaFPC.push(['data5']);
        dataToleranciaFPI.push(['data6']);
        dataToleranciaP.push(['data7']);

        dataContrato.push(['data2']);
        dataTolerancia.push(['data5']);

        var UltContrato = Contrato[0];
        var UltTolerancia = Tolerancia[0];

        for (i = 0; i < 98; i++)
        {
            // caso nao tenha contrato, maximo vira contrato para termos indicacao de periodo (gráfico área)
            if( Contrato[i] == 0 && Periodo[i] != 3 && IDTipoGrafico_Demanda == 1 )
            {
                Contrato[i] = maximoDem;
            }

            // X
            dataX.push(Dias_Demanda[i]);

            // demanda
            dataDemanda.push([Demanda[i]]);

            if( Periodo[i] == 0 )
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([0]);
                dataContratoP.push([Contrato[i]]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([Tolerancia[i]]);
            }
            else if( Periodo[i] == 1 )
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([Contrato[i]]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([Tolerancia[i]]);
                dataToleranciaP.push([0]);
            }
            else if( Periodo[i] == 2 )
            {
                dataContratoFPC.push([Contrato[i]]);
                dataContratoFPI.push([0]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([Tolerancia[i]]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([0]);
            }
            else
            {
                dataContratoFPC.push([0]);
                dataContratoFPI.push([0]);
                dataContratoP.push([0]);

                dataToleranciaFPC.push([0]);
                dataToleranciaFPI.push([0]);
                dataToleranciaP.push([0]);

                // utilizo ultimo contrato, como contrato
                Contrato[i] = UltContrato;
                Tolerancia[i] = UltTolerancia;
            }

            // copia ultimo contrato
            UltContrato = Contrato[i];
            UltTolerancia = Tolerancia[i];

            dataContrato.push([Contrato[i]]);
            dataTolerancia.push([Tolerancia[i]]);
        }

        // valores label X
        labelX.push(Dias_Demanda[1]);
        for (i = 12; i <= 96; i+=12)
        {
            labelX.push(Dias_Demanda[i]);
        }

        // area
        if (IDTipoGrafico_Demanda == 1)
        {
            var Data = [dataX,dataDemanda,dataContratoFPC,dataContratoFPI,dataContratoP,dataToleranciaFPC,dataToleranciaFPI,dataToleranciaP];

            c3.generate({

                bindto: '#demanda-chart',
                size: {
                    height: 300
                },
                padding: {
                    top: 8,
                    right: 10,
                    bottom: 30,
                    left: 43
                },
                data: {
                    x: 'x',
                    xFormat : '%Y-%m-%d %H:%M:%S',
                    columns: Data,
                    types: {
                        data1: 'area-step',
                        data2: 'area-step',
                        data3: 'area-step',
                        data4: 'area-step',
                        data5: 'area-step',
                        data6: 'area-step',
                        data7: 'area-step'
                    },
                    color: function (color, d) {
                        if (typeof d.index === 'undefined') { return color; }

                        if( Periodo[d.index] == 0 )
                            return '#FF0000';

                        if( Periodo[d.index] == 1 )
                            return '#007F00';

                        if( Periodo[d.index] == 2 )
                            return '#1C84C6';

                        return '#007F00';
                    },
                    colors: {
                        data1: '#001F00',
                        data2: '#1C84C6',
                        data3: '#007F00',
                        data4: '#FF0000',
                        data5: '#1C84C6',
                        data6: '#007F00',
                        data7: '#FF0000'
                    },
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                legend: {
                    show: false
                },
                axis: {
                    x:  {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            values: labelX,
                            format: function (x) {

                                // horas e minutos
                                var hours = x.getHours();
                                var minutes = x.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                var strTime = hours + ':' + minutes;

                                // verifica se nao deve mostrar label
                                if(hours==3 || hours==9 || hours==15 || hours==21)
                                    return "";

                                // retorna hora e minuto
                                return strTime;
                            }
                        },
                        // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                        // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                        padding: -1000*60*6
                    },
                    y:  {
                        max: maximoDem,
                        padding: {
                            top: 0,
                            bottom: 0
                        },
                        tick: {
                            outer: false,
                            format: function (d) {

                                if( maximoDem < 100)
                                {
                                    return d.toFixed(1);
                                }

                                return parseInt(d);
                            }
                        }
                    }
                },
                grid: {
                    x: {
                        show: false
                    }
                },
                bar: {
                    width: { ratio: 0.15 },
                },
                tooltip: {
                    format: {
                        /*...*/
                    },
                    contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                        var $$ = this, config = $$.config,
                            titleFormat = config.tooltip_format_title || defaultTitleFormat,
                            nameFormat = config.tooltip_format_name || function (name) { return name; },
                            valueFormat = config.tooltip_format_value || defaultValueFormat,
                            text, i, title, value, name, bgcolor;

                        for (i = 0; i < 4; i++) {

                            if (! text) {

                                // split string and create array.
                                var arr = Dias_Demanda[d[i].index].split(/-|\s|:/);

                                // decrease month value by 1
                                var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                                // horas e minutos
                                var hours = data.getHours();
                                var minutes = data.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                title = hours + ':' + minutes;

                                text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                            }

                            // periodo
                            switch(Periodo[d[0].index])
                            {
                                case 0:
                                    periodo = PONTA;
                                    bgcolor = '#FF0000'
                                    break;

                                case 1:
                                    periodo = FPONTA_IND;
                                    bgcolor = '#007F00';
                                    break;

                                case 2:
                                    periodo = FPONTA_CAP;
                                    bgcolor = '#1C84C6';
                                    break;

                                default:
                                    periodo = "---";
                                    bgcolor = '#000000';
                                    break;
                            }

                            // nome
                            switch(i)
                            {
                                case 0:
                                    name = DEMANDA_ATIVA;
                                    unit = UnidadeDemanda;
                                    value = Demanda[d[0].index].toFixed(1);
                                    break;

                                case 1:
                                    name = CONTRATO;
                                    unit = UnidadeDemanda;
                                    value = Contrato[d[1].index].toFixed(1);
                                    break;

                                case 2:
                                    name = TOLERANCIA;
                                    unit = UnidadeDemanda;
                                    value = Tolerancia[d[4].index].toFixed(1);
                                    break;

                                case 3:
                                    name = PERIODO;
                                    unit = '';
                                    value = periodo;
                                    break;
                            }

                            value = value.replace('.', ',');

                            // verifica se sem registro
                            if( Periodo[d[0].index] == 3 )
                            {
                                value = '---';
                            }

                            if(i==3)
                            {
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + "</td>";
                                text += "</tr>";
                            }
                            else
                            {
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + unit + "</td>";
                                text += "</tr>";
                            }
                        }

                        return text + "</table>";
                    }
                }
            });
        }
        else
        {
            var Data = [dataX,dataDemanda,dataContrato,dataTolerancia];

            c3.generate({

                bindto: '#demanda-chart',
                size: {
                    height: 300
                },
                padding: {
                    top: 8,
                    right: 6,
                    bottom: 30,
                    left: 43
                },
                data: {
                    x: 'x',
                    xFormat : '%Y-%m-%d %H:%M:%S',
                    columns: Data,
                    types: {
                        data1: 'bar',
                        data2: 'step',
                        data5: 'step',
                    },
                    color: function (color, d) {
                        if (typeof d.index === 'undefined') { return color; }

                        if( Periodo[d.index] == 0 )
                            return '#FF0000';

                        if( Periodo[d.index] == 1 )
                            return '#007F00';

                        if( Periodo[d.index] == 2 )
                            return '#1C84C6';

                        return '#007F00';
                    },
                    colors: {
                        data2: '#00007F',
                        data5: '#00007F',
                    },
                },
                transition: {
                    duration: 0
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                legend: {
                    show: false
                },
                axis: {
                    x:  {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            values: labelX,
                            format: function (x) {

                                // horas e minutos
                                var hours = x.getHours();
                                var minutes = x.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                var strTime = hours + ':' + minutes;

                                // verifica se nao deve mostrar label
                                if(hours==3 || hours==9 || hours==15 || hours==21)
                                    return "";

                                // retorna hora e minuto
                                return strTime;
                            }
                        },
                        // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                        // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                        padding: -1000*60*6
                    },
                    y:  {
                        max: maximoDem,
                        padding: {
                            top: 0,
                            bottom: 0
                        },
                        tick: {
                            outer: false,
                            format: function (d) {

                                if( maximoDem < 100)
                                {
                                    return d.toFixed(1);
                                }

                                return parseInt(d);
                            }
                        }
                    }
                },
                grid: {
                    x: {
                        show: false
                    }
                },
                bar: {
                    width: { ratio: 0.058 },
                },
                tooltip: {
                    format: {
                        /*...*/
                    },
                    contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                        var $$ = this, config = $$.config,
                            titleFormat = config.tooltip_format_title || defaultTitleFormat,
                            nameFormat = config.tooltip_format_name || function (name) { return name; },
                            valueFormat = config.tooltip_format_value || defaultValueFormat,
                            text, i, title, value, name, bgcolor;

                        for (i = 0; i < 4; i++) {

                            if (! text) {

                                // split string and create array.
                                var arr = Dias_Demanda[d[0].index].split(/-|\s|:/);

                                // decrease month value by 1
                                var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                                // horas e minutos
                                var hours = data.getHours();
                                var minutes = data.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                title = hours + ':' + minutes;

                                text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                            }

                            // periodo
                            switch(Periodo[d[0].index])
                            {
                                case 0:
                                    periodo = PONTA;
                                    bgcolor = '#FF0000'
                                    break;

                                case 1:
                                    periodo = FPONTA_IND;
                                    bgcolor = '#007F00';
                                    break;

                                case 2:
                                    periodo = FPONTA_CAP;
                                    bgcolor = '#1C84C6';
                                    break;

                                default:
                                    periodo = "---";
                                    bgcolor = '#000000';
                                    break;
                            }

                            // nome
                            switch(i)
                            {
                                case 0:
                                    name = DEMANDA_ATIVA;
                                    unit = UnidadeDemanda;
                                    value = Demanda[d[0].index].toFixed(1);
                                    break;

                                case 1:
                                    name = CONTRATO;
                                    unit = UnidadeDemanda;
                                    value = Contrato[d[0].index].toFixed(1);
                                    break;

                                case 2:
                                    name = TOLERANCIA;
                                    unit = UnidadeDemanda;
                                    value = Tolerancia[d[0].index].toFixed(1);
                                    break;

                                case 3:
                                    name = PERIODO;
                                    unit = '';
                                    value = periodo;
                                    break;
                            }

                            value = value.replace('.', ',');

                            // verifica se sem registro
                            if( Periodo[d[0].index] == 3 )
                            {
                                value = '---';
                            }

                            if(i>0)
                            {
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + "</td>";
                                text += "</tr>";
                            }
                            else
                            {
                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[0].id + "'>";
                                text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                                text += "<td class='value'>" + value + " " + unit + "</td>";
                                text += "</tr>";
                            }
                        }
                        return text + "</table>";
                    }
                }
            });
        }


        //
        // FATOR DE POTENCIA
        //
        dataX = [];
        labelX = [];
        var dataReferenciaFPC = [];
        var dataReferenciaFPI = [];
        var dataReferenciaP = [];
        var dataFatPot = [];
        var dataFatPotUm = [];

        // copia valores
        var FatPot = @Html.Raw(Json.Encode(@ViewBag.FatPot));
        var Posto = @Html.Raw(Json.Encode(@ViewBag.Posto));
        var Dias_FatPot = @Html.Raw(Json.Encode(@ViewBag.Dias_FatPot));
        var FatPotMin = @Html.Raw(Json.Encode(@ViewBag.FatPotMin));

        dataX.push('x');
        dataFatPot.push(['data1']);
        dataReferenciaFPC.push(['data2']);
        dataReferenciaFPI.push(['data3']);
        dataReferenciaP.push(['data4']);
        dataFatPotUm.push(['data5']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias_FatPot[i]);

            // linha de fatpot um
            dataFatPotUm.push([0.0]);

            // converte para nova escala
            if( FatPot[i] < 0 )
            {
                dataFatPot.push([(1.0 - (-1.0*FatPot[i])) * -1.0]);
            }
            else
            {
                dataFatPot.push([(1.0 - FatPot[i])]);
            }

            // periodo
            switch(Posto[i])
            {
                case 0:
                    dataReferenciaP.push([0.08]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([0]);
                    break;

                case 1:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0.08]);
                    dataReferenciaFPC.push([0]);
                    break;

                case 2:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([-0.08]);
                    break;

                default:
                    dataReferenciaP.push([0]);
                    dataReferenciaFPI.push([0]);
                    dataReferenciaFPC.push([0]);
                    break;

            }
        }

        // prepara escala eixo Y
        var maximo = (1.0 - FatPotMin);
        var minimo = -1.0 * maximo;

        // valores label X
        labelX.push(Dias_FatPot[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias_FatPot[i]);
        }

        var Data = [dataX,dataFatPot,dataReferenciaFPC,dataReferenciaFPI,dataReferenciaP,dataFatPotUm];

        c3.generate({
            bindto: '#fatpot-chart',
            size: {
                height: 330
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 41
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    data1: 'bar',
                    data2: 'area-step',
                    data3: 'area-step',
                    data4: 'area-step',
                    data5: 'step'
                },
                color: function (color, d) {
                    if (typeof d.index === 'undefined') { return color; }

                    var cor = '#007F00'

                    switch(Posto[d.index])
                    {
                        case 0:
                            cor = '#FF0000'
                            break;
                        case 1:
                            cor = '#007F00'
                            break;
                        case 2:
                            cor = '#1C84C6'
                            break;
                    }

                    return cor;
                },
                colors: {
                    data1: '#001F00',
                    data2: '#1C84C6',
                    data3: '#007F00',
                    data4: '#FF0000',
                    data5: '#000000'
                },
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x: {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    max: maximo,
                    min: minimo,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            var valor;

                            // converte para nova escala
                            if( d < 0 )
                            {
                                valor = (1.0 - (-1.0*d)) * -1.0;
                            }
                            else
                            {
                                valor = (1.0 - d);
                            }

                            return valor.toFixed(3);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            bar: {
                width: { ratio: 0.25 },
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;

                    for (i = 0; i < d.length; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Dias_FatPot[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1AB394'>" + title + "</th></tr>" : "");
                        }

                        // verifica se sem registro
                        if( Posto[d[i].index] == 3 )
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                name = 'Sem registro';
                                value = '---';

                                text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                                text += "<td class='name'>" + name + "</td>";
                                text += "<td class='value'>" + value + "</td>";
                                text += "</tr>";
                            }
                        }
                        else
                        {
                            // verifica se barra
                            if( i == 0 )
                            {
                                switch(Posto[d[i].index])
                                {
                                    case 0:
                                        name = PONTA;
                                        break;
                                    case 1:
                                    default:
                                        name = FPONTA_IND;
                                        break;
                                    case 2:
                                        name = FPONTA_CAP;
                                        break;
                                }

                                value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                            }

                            // verifica se referencia
                            if( i == 1 )
                            {
                                name = REFERENCIA;

                                // verifica se FPC
                                if(Posto[d[i].index] == 2)
                                {
                                    // pego valor referencia FPC
                                    value = valueFormat(d[1].value, d[1].ratio, d[1].id, d[1].index);
                                }
                                else
                                {
                                    // pego valor referencia FPI
                                    value = valueFormat(d[2].value, d[2].ratio, d[2].id, d[2].index);
                                }
                            }

                            // nao desenha segundo contrato
                            if( i > 1 )
                            {
                                continue;
                            }

                            switch(Posto[d[i].index])
                            {
                                case 0:
                                    bgcolor = '#FF0000';
                                    break;
                                case 1:
                                default:
                                    bgcolor = '#007F00';
                                    break;
                                case 2:
                                    bgcolor = '#1C84C6';
                                    break;
                            }

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                    }
                    return text + "</table>";
                }
            }
        });



        // fatura
        var Fatura_Total = @Html.Raw(Json.Encode(@ViewBag.Fatura_Total));
        var Porc_Demanda = @Html.Raw(Json.Encode(@ViewBag.Porc_Demanda));
        var Porc_Consumo = @Html.Raw(Json.Encode(@ViewBag.Porc_Consumo));
        var Porc_Imposto = @Html.Raw(Json.Encode(@ViewBag.Porc_Imposto));
        var Porc_Multa = @Html.Raw(Json.Encode(@ViewBag.Porc_Multa));

        if( Fatura_Total > 0.0 )
        {
            var chart = c3.generate({
                bindto: '#fatura-chart',
                size: {
                    height: 330
                },
                padding: {
                    top: 0,
                    right: 10,
                    bottom: 0,
                    left: 10
                },
                legend: {
                    position: 'right'
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                data: {
                    columns: [
                        [DEMANDA, Porc_Demanda],
                        [CONSUMO, Porc_Consumo],
                        [IMPOSTO, Porc_Imposto],
                        [MULTA, Porc_Multa],
                    ],
                    type: 'pie',
                    colors: {
                        Demanda: '#007F00',
                        Consumo: '#00007F',
                        Imposto: '#FF7F0E',
                        Multa: '#FF0000',
                    },
                    onclick: function (d, i) { console.log("onclick", d, i); },
                    onmouseover: function (d, i) { console.log("onmouseover", d, i); },
                    onmouseout: function (d, i) { console.log("onmouseout", d, i); }
                },
                pie: {
                    label: {
                        format: function (value, ratio, id) {
                            if(id==DEMANDA) return DEMANDA_ABV;
                            if(id==CONSUMO) return CONSUMO_ABV;
                            if(id==IMPOSTO) return IMPOSTO_ABV;
                            if(id==MULTA) return MULTA_ABV;
                            return d3.format('$')(value);
                        }
                    }
                }
            });
        }


        // eventos
        $('.dataTables-eventos').DataTable({
            "iDisplayLength": 4,
            dom: 'ftp',

            "aoColumnDefs": [
                        {
                            "aTargets": [1],
                            "bVisible": false,
                            "bSortable": true
                        },
                        {
                            "aTargets": [2],
                            "bVisible": true,
                            "bSortable": false
                        },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "25%" },
            { sWidth: "5%" },
            { sWidth: "5%" },
            { sWidth: "80%" }
            ],
            'order': [0, 'desc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });


        function formatIcon(icon) {

            if (!icon.id) {
                return icon.text;
            }
            var originalOption = icon.element;
            var $icon = $('<i style="font-size:16px;" class="fa ' + $(originalOption).data('icon') + '"></i><span>' + icon.text + '</span>');

            return $icon;

        };

        $(".select2_tipo").select2({
            minimumResultsForSearch: Infinity,
            width: "100%",
            templateResult: formatIcon,
            templateSelection: formatIcon
        });

        piscando();
    });

    function SelecionouTipo() {

        // pega tipo selecionado
        //var tipo = document.getElementById("TiposEvento").selectedIndex;
        var tipo = $("#TiposEvento").val();

        // verifica se todos
        if (tipo == 0)
        {
            // procura na tabela
            $('.dataTables-eventos').DataTable().column(1).search("").draw();
        }
        else
        {
            // procura na tabela
            $('.dataTables-eventos').DataTable().column(1).search(tipo).draw();
        }
    }

    function piscando() {

        var tempo = 500; //1000 = 1s

        blinks = document.getElementsByTagName("blink");
        for(var i=0;i<blinks.length;i++){
            if(blinks[i].getAttribute("style")=="VISIBILITY: hidden"){
                blinks[i].setAttribute("style", "VISIBILITY: visible");
            }else{
                blinks[i].setAttribute("style", "VISIBILITY: hidden");
            }
        }
        setTimeout('piscando()', tempo);
    }


    </script>
}





