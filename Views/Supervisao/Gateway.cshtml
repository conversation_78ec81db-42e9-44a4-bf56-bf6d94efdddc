﻿@using System.Globalization
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = "Gateway";
}

<head>
    <title>Gateways</title>
</head>

@{
    int IDTipoGateway = ViewBag.GatewayTipo;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    @{
                        if (IDTipoGateway == TIPO_GATEWAY.GATE_E)
                        {
                            @Html.Partial("_Gateway_GateE")
                        }
                        else
                        {
                            @Html.Partial("_Gateway_Gate")
                        }
                    }

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

    <script type="text/javascript">

        $(document).ready(function () {

        });

        $(".bottom").tooltip({
            placement: "bottom"
        });

    </script>
}

