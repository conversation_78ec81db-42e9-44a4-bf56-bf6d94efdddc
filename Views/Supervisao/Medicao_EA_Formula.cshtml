﻿@using System.Globalization
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicao;

    int IDConsultor = ViewBag._IDConsultor;
}

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="row">
        <div class="col-lg-12">
            <div class="superv-content">

                <div class="col-lg-4 col-md-12">
                    <div class="row">
                        <div class="col-lg-12 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-ea">
                                    <h4>@ViewBag.NomeGrandeza</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.MedioDia</h5>
                                    <h1>@ViewBag.Dia_Atual_Media <span>@ViewBag.UnidadeGrandeza</span></h1>
                                    <h6>@ViewBag.DiaAtual</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela superv-ea">
                                <table class="table no-margins" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th style="width:50%;"></th>
                                            <th style="width:50%;">@ViewBag.NomeGrandeza</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td style="text-align:center">@SmartEnergy.Resources.SupervisaoTexts.Maximo<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Dia_Atual_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Dia_Atual_MaxDataHora)</small></td>
                                        </tr>
                                        <tr>
                                            <td style="text-align:center">@SmartEnergy.Resources.SupervisaoTexts.Minimo<br /><small>&nbsp;</small></td>
                                            <td>@ViewBag.Dia_Atual_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Dia_Atual_MinDataHora)</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8 col-md-12">
                    <div class="row">
                        <div class="superv-grafico">
                            <div class="flot-chart">
                                <div class="postlet-grafico-ea" id="grafico-ea"></div>
                            </div>
                            <div class='chart-legend'>
                                <div class='legend-scale-dem'>
                                    <ul class='legend-labels'>
                                        <li><span style='background:#1C84C6;'></span>@ViewBag.NomeGrandeza</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="wrapper wrapper-content">

    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    <div class="col-lg-6 col-md-12">
                        <div class="row">
                            <div class="superv-grafico-fatura">
                                <div class="flot-chart">
                                    <div id="ea-chart"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="superv-tabela superv-ea">
                                    <table class="table no-margins">
                                        <thead>
                                            <tr>
                                                <th style="width: 25%;"></th>
                                                <th style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.Minimo</th>
                                                <th style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.Medio</th>
                                                <th style="width: 25%;">@SmartEnergy.Resources.SupervisaoTexts.Maximo</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>@ViewBag.Mes_Atual_Data</td>
                                                <td>@ViewBag.Mes_Atual_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Atual_MinDataHora)</small></td>
                                                <td>@ViewBag.Mes_Atual_Media @ViewBag.UnidadeGrandeza<br /><small>&nbsp;</small></td>
                                                <td>@ViewBag.Mes_Atual_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Atual_MaxDataHora)</small></td>
                                            </tr>
                                            <tr>
                                                <td>@ViewBag.Mes_Anterior1_Data</td>
                                                <td>@ViewBag.Mes_Anterior1_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Anterior1_MinDataHora)</small></td>
                                                <td>@ViewBag.Mes_Anterior1_Media @ViewBag.UnidadeGrandeza<br /><small>&nbsp;</small></td>
                                                <td>@ViewBag.Mes_Anterior1_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Anterior1_MaxDataHora)</small></td>
                                            </tr>
                                            <tr>
                                                <td>@ViewBag.Mes_Anterior2_Data</td>
                                                <td>@ViewBag.Mes_Anterior2_Min @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Anterior2_MinDataHora)</small></td>
                                                <td>@ViewBag.Mes_Anterior2_Media @ViewBag.UnidadeGrandeza<br /><small>&nbsp;</small></td>
                                                <td>@ViewBag.Mes_Anterior2_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.Mes_Anterior2_MaxDataHora)</small></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @{
        // RETHA nao quer que aparece GATEWAY
        if (IDConsultor != CLIENTES_ESPECIAIS.RETHA)
        {

            <div class="row">
                <div class="col-lg-8">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.ConfiguracaoTexts.Formula</h4><br />
                            @ViewBag.Formula
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</h4><br />
                            @ViewBag.GatewayAtualizacao
                        </div>
                    </div>
                </div>
            </div>

        }
    }        
        
    @if( @ViewBag.listaErros.Count > 0 )
    {
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-danger">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Erros</h4><br />
                        <ul style="list-style-type:disc; margin-left: 20px;">
                            @foreach (var erro in @ViewBag.listaErros)
                            {
                                <li>@erro</li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }
    
</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/Supervisao.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/select2")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">
    $(document).ready(function () {

        // entrada analogica
        var dataX = [];
        var labelX = [];
        var dataMedia = [];
        var dataMinima = [];
        var dataMaxima = [];
        var dataZero = [];

        // copia valores
        var Media = @Html.Raw(Json.Encode(@ViewBag.Media));
        var Minima = @Html.Raw(Json.Encode(@ViewBag.Minima));
        var Maxima = @Html.Raw(Json.Encode(@ViewBag.Maxima));
        var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
        var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
        var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

        var minimoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMin));
        var maximoValor = @Html.Raw(Json.Encode(@ViewBag.ValorMax));

        var diferenca = maximoValor - minimoValor;

        dataX.push('x');
        dataMaxima.push(['Max']);
        dataMedia.push(['Score']);
        dataMinima.push(['Min']);
        dataZero.push(['Zero']);

        for (i = 0; i < 26; i++)
        {
            // X
            dataX.push(Dias[i]);

            // linha de zero
            dataZero.push([0.0]);

            dataMedia.push([Media[i]]);
            dataMinima.push([Minima[i]]);
            dataMaxima.push([Maxima[i]]);
        }

        // valores label X
        labelX.push(Dias[1]);
        for (i = 3; i <= 24; i+=3)
        {
            labelX.push(Dias[i]);
        }

        var Data = [dataX,dataMaxima,dataMedia,dataMinima];

        c3.generate({
            bindto: '#grafico-ea',
            size: {
                height: 300
            },
            padding: {
                top: 8,
                right: 10,
                bottom: 30,
                left: 43
            },
            data: {
                x: 'x',
                xFormat : '%Y-%m-%d %H:%M:%S',
                columns: Data,
                types: {
                    Max: 'line',
                    Score: 'line',
                    Min: 'line',
                    //Zero: 'step'
                },
                colors: {
                    Max: 'rgb(200, 217, 240)',
                    Score: 'rgb(120, 178, 235)',
                    Min: 'rgb(200, 217, 240)',
                    //Zero: '#dddddd'
                }
            },
            point: {
                r:2,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis: {
                x:  {
                    type: 'timeseries',
                    tick: {
                        outer: false,
                        values: labelX,
                        format: function (x) {

                            // horas e minutos
                            var hours = x.getHours();
                            var minutes = x.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            var strTime = hours + ':' + minutes;

                            // verifica se nao deve mostrar label
                            if(hours==3 || hours==9 || hours==15 || hours==21)
                                return "";

                            // retorna hora e minuto
                            return strTime;
                        }
                    },
                    // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                    // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                    padding: -1000*60*28
                },
                y:  {
                    min: minimoValor,
                    max: maximoValor,
                    padding: {
                        top: 0,
                        bottom: 0
                    },
                    tick: {
                        outer: false,
                        format: function (d) {

                            if( diferenca < 10)
                            {
                                return d.toFixed(2);
                            }

                            if( diferenca < 100)
                            {
                                return d.toFixed(1);
                            }

                            return parseInt(d);
                        }
                    }
                }
            },
            grid: {
                x: {
                    show: false
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    for (i = 0; i < 3; i++) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {

                            // split string and create array.
                            var arr = Dias[d[i].index].split(/-|\s|:/);

                            // decrease month value by 1
                            var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                            // horas e minutos
                            var hours = data.getHours();
                            var minutes = data.getMinutes();

                            hours = hours < 10 ? '0'+hours : hours;
                            minutes = minutes < 10 ? '0'+minutes : minutes;
                            title = hours + ':' + minutes;

                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        switch(i)
                        {
                            case 2:     // minimo
                                name = "Mínimo";
                                break;

                            case 1:     // medio
                                name = "Médio";
                                break;

                            case 0:     // maximo
                                name = "Máximo";
                                break;
                        }

                        value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + d[i].value.toFixed(2) + " " + UnidadeGrandeza + "</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }
        });

        d3.selection.prototype.moveToFront = function () {
            return this.each(function () {
                this.parentNode.appendChild(this);
            });
        };

        d3.select("svg g.c3-grid").moveToFront();


        // meses

        // copia valores
        var Mes_Atual_Data = @Html.Raw(Json.Encode(ViewBag.Mes_Atual_DataMes));
        var Mes_Atual_Max = @Html.Raw(Json.Encode(ViewBag.Mes_Atual_MaxN));
        var Mes_Atual_Media = @Html.Raw(Json.Encode(ViewBag.Mes_Atual_MediaN));
        var Mes_Atual_Min = @Html.Raw(Json.Encode(ViewBag.Mes_Atual_MinN));

        var Mes_Anterior1_Data = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior1_DataMes));
        var Mes_Anterior1_Max = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior1_MaxN));
        var Mes_Anterior1_Media = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior1_MediaN));
        var Mes_Anterior1_Min = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior1_MinN));

        var Mes_Anterior2_Data = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior2_DataMes));
        var Mes_Anterior2_Max = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior2_MaxN));
        var Mes_Anterior2_Media = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior2_MediaN));
        var Mes_Anterior2_Min = @Html.Raw(Json.Encode(ViewBag.Mes_Anterior2_MinN));


        var x = ['x', Mes_Anterior2_Data, Mes_Anterior1_Data, Mes_Atual_Data];
        var Min = ['Min', Mes_Anterior2_Min, Mes_Anterior1_Min, Mes_Atual_Min];
        var Med = ['Med', Mes_Anterior2_Media, Mes_Anterior1_Media, Mes_Atual_Media];
        var Max = ['Max', Mes_Anterior2_Max, Mes_Anterior1_Max, Mes_Atual_Max];

        // verifica maximo
        var maxValor = Mes_Atual_Max;

        if( Mes_Anterior1_Max > maxValor)
        {
            maxValor = Mes_Anterior1_Max;
        }

        if( Mes_Anterior2_Max > maxValor)
        {
            maxValor = Mes_Anterior2_Max;
        }

        maxValor = maxValor * 1.1;

        if (maxValor < 0.0)
        {
            maxValor = 0.0;
        }

        c3.generate({
            bindto: '#ea-chart',
            size: {
                height: 230
            },
            padding: {
                top: 8,
                right: 0,
                bottom: 20,
                left: 60
            },
            data: {
                x: 'x',
                columns: [
                    x,
                    Min, Med, Max
                ],
                type: 'bar'
            },
            point: {
                show: false,
                sensitivity: 1000
            },
            legend: {
                show: false
            },
            axis:{
                rotated: false,
                x : {
                    type : 'categories',
                    tick: {
                        fit: true
                    }
                },
                y : {
                    show: true,
                    max: maxValor
                }
            },
            color: {
                pattern: ['#D7E8F8', '#4284B2', '#00007f']
            },
            grid: {
                y: {
                    lines: [{value:0}]
                }
            },
            tooltip: {
                format: {
                    /*...*/
                },
                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                    var $$ = this, config = $$.config,
                        titleFormat = config.tooltip_format_title || defaultTitleFormat,
                        nameFormat = config.tooltip_format_name || function (name) { return name; },
                        valueFormat = config.tooltip_format_value || defaultValueFormat,
                        text, i, title, value, name, bgcolor;
                    for (i = d.length-1; i >= 0; i--) {
                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                        if (! text) {
                            title = titleFormat ? titleFormat(d[i].x) : d[i].x;
                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                        }

                        switch(i)
                        {
                            case 0:     // minimo
                                name = "Mínimo";
                                value = d[i].value.toFixed(2);
                                break;

                            case 1:     // medio
                                name = "Médio";
                                value = d[i].value.toFixed(2);
                                break;

                            case 2:     // maximo
                                name = "Máximo";
                                var soma = d[i].value + d[0].value;
                                value = soma.toFixed(2);
                                break;
                        }

                        value = d[i].value.toFixed(2);
                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                        text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                        text += "<td class='value'>" + value + " " + UnidadeGrandeza + "</td>";
                        text += "</tr>";
                    }
                    return text + "</table>";
                }
            }

        });


        piscando();
    });

    function piscando() {

        var tempo = 500; //1000 = 1s

        blinks = document.getElementsByTagName("blink");
        for(var i=0;i<blinks.length;i++){
            if(blinks[i].getAttribute("style")=="VISIBILITY: hidden"){
                blinks[i].setAttribute("style", "VISIBILITY: visible");
            }else{
                blinks[i].setAttribute("style", "VISIBILITY: hidden");
            }
        }
        setTimeout('piscando()', tempo);
    }


</script>
}





