﻿@model IEnumerable<SmartEnergy.Controllers.GateE_TesteCampo_Resultado>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes
@using SmartEnergy.Controllers

@{
    ViewBag.Title = "Teste de Campo";
}

<head>
    <title>Medições</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }

    </style>
</head>

@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
    int IDConsultor = ViewBag._IDConsultor;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>SmartGate E [Teste de Campo]</h4>
                </div>
                <div class="panel-body">

                    <table class="table table-striped table-bordered table-hover dataTables-medicoes">
                        <thead>
                            <tr>
                                <th>Gateway GateE</th>
                                <th>Medição GateE</th>
                                <th>Último Registro</th>
                                <th>Gateway GateX</th>
                                <th>Medição GateX</th>
                                <th>Último Registro</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (GateE_TesteCampo_Resultado medicao in Model)
                            {
                                string UltimaDataHora_E = string.Format("{0:G}", medicao.UltimaDataHora_E);
                                string UltimaDataHora_X = string.Format("{0:G}", medicao.UltimaDataHora_X);

                                if (medicao.UltimaDataHora_E.Year == 2000)
                                {
                                    UltimaDataHora_E = "----------";
                                }

                                if (medicao.UltimaDataHora_X.Year == 2000)
                                {
                                    UltimaDataHora_X = "----------";
                                }

                                <tr>
                                    <td>@string.Format("{0:000000}", medicao.IDGateway_E)<br />@medicao.NomeGateway_E<br />&nbsp;</td>
                                    <td>@string.Format("{0:000000}", medicao.IDMedicao_E)<br />@medicao.NomeMedicao_E<br />&nbsp;</td>
                                    <td>@UltimaDataHora_E<br />&nbsp;</td>
                                    <td>@string.Format("{0:000000}", medicao.IDGateway_X)<br />@medicao.NomeGateway_X<br />&nbsp;</td>
                                    <td>@string.Format("{0:000000}", medicao.IDMedicao_X)<br />@medicao.NomeMedicao_X<br />&nbsp;</td>
                                    <td>@UltimaDataHora_X<br />&nbsp;</td>
                                    <td class="some_desktop link_preto">
                                        <a href='@("/Supervisao/SupervisaoGateE_TesteCampo_Resultado?IDGateway=" + @medicao.IDGateway_E.ToString())' title="Análise">
                                            <i class="fa fa-desktop icones"></i>
                                        </a>
                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-en.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "es-AR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-es-AR.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
        <script type="text/javascript" language="javascript" src="~/Scripts/declaracoes/Traducao-pt-br.js"></script>
    }

    <script type="text/javascript">
    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-")
                {
                    x = "-1";
                }
                else
                {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-medicoes').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                { "aTargets": [0], "sType": "portugues" },
                { "aTargets": [1], "sType": "portugues" },
                { "aTargets": [2], "sType": "portugues" },
                { "aTargets": [3], "sType": "portugues" },
                { "aTargets": [4], "sType": "portugues" },
                { "aTargets": [5], "sType": "portugues" },
                { "aTargets": [6], "bVisible": true, "bSortable": false, "bSearchable": false, },
            ],
            "aoColumns": [
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "15%" },
            { sWidth: "10%" }
            ],
            "order": [[0, "asc"]],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });
    });


    </script>

}


