﻿@model IEnumerable<SmartEnergyLib.SQL.SupervGatewaysDominio>

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = "Gateways IoT";
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #333333 !important;
    }

    .icones-danger {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #ED5565 !important;
    }

    .icones-warning {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #f8ac59 !important;
    }

    .icones-primary {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #1ab394 !important;
    }

    .tipo1-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo2-bg {
        background-color: #C0C0C0 !important;
        color: #000000 !important;
    }

    .tipo3-bg {
        background-color: #A9A9A9 !important;
        color: #ffffff !important;
    }

    .tipo4-bg {
        background-color: #696969 !important;
        color: #ffffff !important;
    }

    .tipo5-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo6-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .dataTables-gateways tr.even:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    .dataTables-gateways tr.odd:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    .tooltip-inner {
        position: relative;
        display: inline-block;
        max-width: 350px;
        /* If max-width does not work, try using width instead */
        white-space: pre-wrap;
        text-align: left;
    }
</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>Gateways IoT</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <table id="example" class="table table-bordered table-hover dataTables-gateways">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.ClientesNomeFantasia</th>
                                        <th>ID</th>
                                        <th>Gateway</th>
                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.Modelo | @SmartEnergy.Resources.ConfiguracaoTexts.Versao</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                        <th>Última Conexão IoT</th>
                                        <th>Sinal | IP | Operadora (Tecnologia)</th>
                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.AcessoRemoto</th>
                                        <th>Conexão IoT</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{
                                        foreach (SupervGatewaysDominio gateway in Model)
                                        {
                                            // copia data e hora
                                            string DataHora = String.Format("{0:G}", gateway.DataHora);
                                            string DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", gateway.DataHora);

                                            // copia data e hora conexao IoT
                                            string DataEq = String.Format("{0:G}", gateway.DataEq);
                                            string DataEq_Sort = String.Format("{0:yyyyMMddHHmmss}", gateway.DataEq);

                                            if (!gateway.GatewayConectada_IoT)
                                            {
                                                DataEq = "---";
                                                DataEq_Sort = "20000101000000";
                                            }

                                            // dados do celular
                                            string infos_celular = "";

                                            if (gateway.OP != null)
                                            {
                                                infos_celular = "ICC [" + ((gateway.ICC.Length == 0) ? "---" : gateway.ICC) + "]<br>";
                                                infos_celular += "IMEI [" + ((gateway.IMEI.Length == 0) ? "---" : gateway.IMEI) + "]<br>";
                                                infos_celular += "FAB [" + ((gateway.FAB.Length == 0) ? "---" : gateway.FAB) + "]<br>";
                                                infos_celular += "MOD [" + ((gateway.MOD.Length == 0) ? "---" : gateway.MOD) + "]<br>";
                                                infos_celular += "OP [" + ((gateway.OP.Length == 0) ? "---" : gateway.OP) + "]<br>";
                                                infos_celular += "GMS_TEC [" + ((gateway.GSM_TEC.Length == 0) ? "---" : gateway.GSM_TEC) + "]<br>";
                                                infos_celular += "MCC [" + ((gateway.MCC.Length == 0) ? "---" : gateway.MCC) + "]<br>";
                                                infos_celular += "MNC [" + ((gateway.MNC.Length == 0) ? "---" : gateway.MNC) + "]<br>";
                                                infos_celular += "LAC [" + ((gateway.LAC.Length == 0) ? "---" : gateway.LAC) + "]<br>";
                                                infos_celular += "CID [" + ((gateway.CID.Length == 0) ? "---" : gateway.CID) + "]<br>";
                                            }

                                            int sinal = gateway.Sinal;
                                            string SinalTexto = "Sinal Fraco";
                                            string SinalCor = "red";
                                            string sinalStr = "-";
                                            string gatewayIP = gateway.IP;

                                            if (sinal >= 38 && sinal < 50)
                                            {
                                                SinalTexto = "Sinal Médio Fraco";
                                                SinalCor = "orange";
                                            }

                                            if (sinal >= 50 && sinal < 68)
                                            {
                                                SinalTexto = "Sinal Médio";
                                                SinalCor = "orange";
                                            }

                                            if (sinal >= 68 && sinal <= 100)
                                            {
                                                SinalTexto = "Sinal Forte";
                                                SinalCor = "green";
                                            }

                                            if (sinal >= 100)
                                            {
                                                sinal = 999;
                                            }

                                            if (sinal > 0)
                                            {
                                                sinalStr = string.Format("Sinal {0}%", gateway.Sinal);
                                            }
                                            else
                                            {
                                                SinalTexto = "Ethernet";
                                                SinalCor = "blue";
                                                sinalStr = "Ethernet";
                                                gatewayIP = "";
                                            }

                                            if (infos_celular.Length == 0)
                                            {
                                                infos_celular = "Sem informações";
                                            }

                                            string operadora = " ";

                                            if (gateway.OP != null)
                                            {
                                                if (gateway.OP.Length > 0)
                                                {
                                                    operadora = gateway.OP;
                                                }

                                                if (gateway.GSM_TEC.Length > 0)
                                                {
                                                    operadora += " (" + gateway.GSM_TEC + ")";
                                                }
                                            }

                                            // acesso remota
                                            string acesso_remoto = gateway.AcessoRemoto ? "Habilitado" : "Desabilitado";

                                            <tr style="cursor:pointer;" onclick="location.href='@("/Supervisao/Gateway?IDGateway=" + @gateway.IDGateway.ToString())'">
                                                <td>@gateway.IDCliente</td>
                                                <td>@gateway.Fantasia.ToUpper()</td>
                                                <td>@gateway.IDGateway</td>
                                                <td>@gateway.Nome</td>
                                                <td>@gateway.ModeloEq<br />@gateway.VersaoEq</td>
                                                <td><span style="display:none;">@DataHora_Sort    </span>@DataHora</td>
                                                <td><span style="display:none;">@DataEq_Sort    </span>@DataEq</td>
                                                <td>
                                                    <span style="display:none;">@string.Format("{0:000}", gateway.Sinal)</span>
                                                    <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@SinalTexto"><font color="@SinalCor"><b>@sinalStr</b></font></span><br />
                                                    <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@infos_celular">@gatewayIP</span><br />@operadora
                                                </td>
                                                <td>@acesso_remoto</td>

                                                @{
                                                    if (gateway.GatewayConectada_IoT)
                                                    {
                                                        <td><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="Gateway conectada no Servidor IoT"><i class="fa fa-chain" style="font-size:20px; color:green"></i>&nbsp;&nbsp;</span></td>
                                                    }
                                                    else
                                                    {
                                                        <td><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="Gateway não conectada no Servidor IoT"><i class="fa fa-chain-broken" style="font-size:20px; color:red"></i>&nbsp;&nbsp;</span></td>
                                                    }
                                                }

                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/Content/plugins/dataTables/dataTablesStyles")
}    

@section Scripts {
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/jqueryui")
    
    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-gateways').DataTable({
                "iDisplayLength": 10,
                dom: 'Bftp',
                buttons: [
                    {
                        extend: 'excelHtml5',
                        filename: 'Gateways',
                        title: 'Gateways',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                    {
                        extend: 'pdfHtml5',
                        filename: 'Gateways',
                        title: 'Gateways',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                ],

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "numero" },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "numero" },
                            { "aTargets": [3], "sType": "portugues" },
                            { "aTargets": [4], "sType": "portugues" },
                            { "aTargets": [5], "sType": "portugues" },
                            { "aTargets": [6], "sType": "portugues" },
                            { "aTargets": [7], "sType": "portugues" },
                            { "aTargets": [8], "sType": "portugues" },
                            { "aTargets": [9], "sType": "portugues" },
                ],
                "aoColumns": [
                { sWidth: "4%" },
                { sWidth: "14%" },
                { sWidth: "4%" },
                { sWidth: "14%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "11%" },
                { sWidth: "12%" },
                { sWidth: "8%" },
                { sWidth: "8%" },
                ],
                'order': [3, 'asc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

        });

        $(".bottom").tooltip({
            placement: "bottom"
        });

    </script>
}

