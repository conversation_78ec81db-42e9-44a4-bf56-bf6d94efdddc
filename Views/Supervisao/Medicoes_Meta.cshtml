﻿@model IEnumerable<SmartEnergyLib.SQL.MedicoesSupervDominio>

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicoes;
}

<head>
    <title>Medi<PERSON><PERSON>es</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }

    </style>
</head>

@{
    int IDTipoAcesso = ViewBag._IDTipoAcesso;
}

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Medicoes</h4>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-medicoes">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.SupervisaoTexts.Grupos</th>
                                <th class="some_minidesktop">@SmartEnergy.Resources.SupervisaoTexts.Unidades</th>
                                <th class="some_minidesktop"></th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.Medicoes</th>
                                <th class="some_minidesktop">Descrição</th>
                                <th>Valor</th>
                                <th>@SmartEnergy.Resources.DashboardTexts.MetaMensal (kWh)</th>
                                <th>%</th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.DataHora</th>
                                <th class="some_desktop">@SmartEnergy.Resources.SupervisaoTexts.Observacoes</th>
                                <th class="some_desktop">Status</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach (MedicoesSupervDominio medicao in Model)
                            {
                                int Status = 0;
                                int Status_Meta = 0;
                                int Status_Atualizacao = 0;
                                
                                // copia data e hora
                                DateTime data_atualizacao = DateTime.Now;

                                string DataHora = "-";
                                string DataHora_Sort = "20000101000000";

                                if (medicao.DataHoraValor != null)
                                {
                                    DataHora = String.Format("{0:G}", medicao.DataHoraValor);
                                    DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", medicao.DataHoraValor);
                                    data_atualizacao = medicao.DataHoraValor;
                                }
                                
                                // diferenca
                                TimeSpan diferenca = DateTime.Now - data_atualizacao;
                                
                                if( diferenca.Days >= 2)
                                {
                                    // status atualizacao
                                    Status_Atualizacao = 1;
                                }
                                                                
                                // consumo e meta
                                var ProjetadoMes = String.Format("{0:#,##0}", medicao.ProjetadoMes);
                                var MetaMes = String.Format("{0:#,##0}", medicao.MetaMes);
                                
                                // verifica se nao eh energia
                                if (medicao.IDTipoMedicao > TIPO_MEDICAO.ENERGIA_FORMULA)
                                {
                                    ProjetadoMes = "-";
                                    MetaMes = "-";
                                    medicao.MetaProjPC = -1.0; 
                                }

                                var MetaProjPC = medicao.MetaProjPC;

                                // consumo  
                                string UnidadeConsumo = "Wh";
                                if (medicao.IDTipoUnidadePotencia == 1)
                                {
                                    UnidadeConsumo = "kWh";
                                }
                                                         
                                string logo;
                                string tipomedicao = "0";
                                string caminho_superv = "Medicao_Energia";
                                string descricao = "-";
                                string valor = "-";

                                switch (medicao.IDTipoMedicao)
                                {
                                    case TIPO_MEDICAO.ENERGIA:

                                        caminho_superv = "Medicao_Energia";
                                        descricao = "Consumo Projetado";
                                        valor = String.Format("{0:#,##0} {1}", ProjetadoMes, UnidadeConsumo);
                                        tipomedicao = "0";

                                        // verifica se cliente com meta
                                        if (medicao.IDTipoSupervisao == 1)
                                        {
                                            caminho_superv = "Medicao_Energia_PDA";
                                        }
                                       
                                        break;

                                    case TIPO_MEDICAO.ENERGIA_FORMULA:

                                        caminho_superv = "Medicao_Energia_Formula";
                                        descricao = "Consumo Projetado";
                                        valor = String.Format("{0:#,##0} {1}", ProjetadoMes, UnidadeConsumo);
                                        tipomedicao = "1";

                                        // verifica se cliente com meta
                                        if (medicao.IDTipoSupervisao == 1)
                                        {
                                            caminho_superv = "Medicao_Energia_PDA_Formula";
                                        }
                                        
                                        break;

                                    case TIPO_MEDICAO.UTILIDADES:

                                        caminho_superv = "Medicao_Utilidades";
                                        descricao = "Consumo Atual";
                                        valor = String.Format("{0:#,##0.00} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "2";

                                        if (medicao.IDIcone == 2)
                                        {
                                            descricao = "Tempo";
                                            valor = String.Format("{0:#,##0} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        }                                        
                                        break;

                                    case TIPO_MEDICAO.UTILIDADES_FORMULA:

                                        caminho_superv = "Medicao_Utilidades_Formula";
                                        descricao = "Consumo Atual";
                                        valor = String.Format("{0:#,##0.00} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "3";

                                        if (medicao.IDIcone == 2)
                                        {
                                            descricao = "Tempo";
                                            valor = String.Format("{0:#,##0} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        }
                                        
                                        break;

                                    case TIPO_MEDICAO.ENTRADA_ANALOGICA:

                                        caminho_superv = "Medicao_EA";
                                        descricao = medicao.NomeGrandeza + " (Média)";
                                        valor = String.Format("{0:#,##0.00} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "5";
                                        
                                        break;

                                    case TIPO_MEDICAO.EA_FORMULA:

                                        caminho_superv = "Medicao_EA_Formula";
                                        descricao = medicao.NomeGrandeza + " (Média)";
                                        valor = String.Format("{0:#,##0.00} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "6";
                                        
                                        break;

                                    case TIPO_MEDICAO.CICLOMETRO:

                                        caminho_superv = "Medicao_Ciclometro";
                                        descricao = "Ciclômetro";
                                        valor = String.Format("{0:00000000} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "4";
                                        
                                        break;

                                    case TIPO_MEDICAO.METEOROLOGIA:

                                        caminho_superv = "Medicao_Meteorologia";
                                        descricao = "Temperatura Atual";
                                        valor = String.Format("{0:#,##0} {1}", medicao.UltimoValor, medicao.UnidadeGrandeza);
                                        tipomedicao = "7";
                                        
                                        break;
                                        
                                }
                                
                                <tr style="cursor:pointer;" onclick="location.href='@("/Supervisao/" + caminho_superv + "?IDCliente=" + @medicao.IDCliente.ToString() + "&IDMedicao=" + @medicao.IDMedicao.ToString())'">
                                    <td>@medicao.IDMedicao<br />&nbsp;</td>
                                    <td class="some_minidesktop">@medicao.NomeGrupoUnidades</td>
                                    <td class="some_minidesktop">@medicao.NomeUnidade</td>

                                    @{
                                        // energia
                                        logo = "/Imagens/icone_lampada24.png";

                                        // energia formula
                                        if (medicao.IDTipoMedicao == TIPO_MEDICAO.ENERGIA_FORMULA)
                                        {
                                            logo = "/Imagens/icone_soma24.png";
                                        }

                                        // utilidades
                                        if (medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES || medicao.IDTipoMedicao == TIPO_MEDICAO.UTILIDADES_FORMULA)
                                        {
                                            if (medicao.IDIcone == 0)
                                            {
                                                logo = "/Imagens/icone_gota24.png";
                                            }

                                            if (medicao.IDIcone == 1)
                                            {
                                                logo = "/Imagens/icone_gas24.png";
                                            }

                                            if (medicao.IDIcone == 2)
                                            {
                                                logo = "/Imagens/icone_relogio24.png";
                                            }
                                        }

                                        // ciclometro
                                        if (medicao.IDTipoMedicao == TIPO_MEDICAO.CICLOMETRO)
                                        {
                                            logo = "/Imagens/icone_ciclo24.png";
                                        }

                                        // estacao metereologica
                                        if (medicao.IDTipoMedicao == TIPO_MEDICAO.METEOROLOGIA)
                                        {
                                            logo = "/Imagens/icone_meteorologia24.png";
                                        }
                                    }

                                    <td class="some_minidesktop" style="text-align:center;"><span style="display:none;">@tipomedicao</span><img src=@logo /></td>
                                    <td>@medicao.NomeMedicao</td>
                                     
                                    <td class="some_minidesktop">@descricao</td>
                                    <td>@valor</td>
                                    <td>@MetaMes</td>
                                    <td>@MetaProjPC</td>
                                    <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>

                                    @{
                                        var Observacao_Tag_Sort = "Z";

                                        if (medicao.IDTag > 0 && medicao.IDTag < 100)
                                        {
                                            Observacao_Tag_Sort = "A_" + medicao.Observacao_Tag;
                                        }

                                        if (medicao.IDTag > 100 && medicao.IDTag < 200)
                                        {
                                            Observacao_Tag_Sort = "B_" + medicao.Observacao_Tag;
                                        }

                                        if (medicao.IDTag > 200 && medicao.IDTag < 300)
                                        {
                                            Observacao_Tag_Sort = "C_" + medicao.Observacao_Tag;
                                        }
                                        
                                        if (medicao.IDTag == 0)
                                        {
                                            <td onclick="ObservacoesMedicao(event, @medicao.IDMedicao);"><span style="display:none;">@Observacao_Tag_Sort</span>&nbsp;</td>
                                        }
                                        else
                                        {
                                            var classe = string.Format("badge-{0}", medicao.Observacao_Badge);
                                            var titulo = string.Format("{0:d}\n{1}", medicao.Observacao_DataHora, medicao.Observacao_Texto);

                                            <td onclick="ObservacoesMedicao(event, @medicao.IDMedicao);"><span style="display:none;">@Observacao_Tag_Sort</span><span class="badge @classe some_desktop" title="@titulo">&nbsp;&nbsp;@medicao.Observacao_Tag&nbsp;&nbsp;</span></td>
                                        }
                                    }

                                    @{
                                        if (MetaProjPC > 100.0)
                                        {
                                            Status_Meta = 1;
                                        }

                                        if (Status_Meta == 0 && Status_Atualizacao == 0)
                                        {
                                            Status = 0;
                                            
                                            <td class="some_desktop"><span class="badge badge-primary" title=" Normal ">&nbsp;&nbsp;Normal&nbsp;&nbsp;</span></td>
                                        }
                                        else
                                        {
                                            <td class="some_desktop">                                            
                                            
                                                @if (Status_Meta == 1)
                                                {
                                                    Status = 2;
                                                    var comentario = String.Format("{0:#,##0.0}", MetaProjPC);
                                                    <span class="badge badge-danger" title="@(" Consumo Projetado maior que a Meta (" + @comentario + "%) ")" >&nbsp;&nbsp;Meta&nbsp;&nbsp;</span>
                                                }

                                                @if (Status_Atualizacao == 1)
                                                {
                                                    Status = 1;
                                                    <span class="badge badge-danger" title=" Última Atualização ocorreu a mais de 48 horas ">&nbsp;&nbsp;Atraso&nbsp;&nbsp;</span>
                                                }

                                            </td>
                                        }
                                        
                                        <td>@Status</td>
                                    }

                                </tr>
                            }

                        </tbody>
                    </table>

                    @Html.Hidden("IDMedSelecionado", 0)
                    @Html.Hidden("IDObservacaoSelecionado", 0)

                    <div class="modal inmodal animated fadeIn" id="ModalObservacoes" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-editar-header">
                                    <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacoes</span>

                                    @if (!isUser.isOperador(IDTipoAcesso) && IDTipoAcesso != 10)
                                    {
                                        <div class="pull-right dashboard-tools link_branco" style="margin-top:-2px;">
                                            <h4>
                                                <a href="#" onclick="javascript:ObservacaoMedicao(event, 0, 0);" id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                                            </h4>
                                        </div>
                                    }
                                    
                                </div>
                                <div class="modal-body">

                                    <div id="Observacoes_resultado" min-height:500px;">
                                        @Html.Partial("_ObservacoesMedicao")
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default" onclick="FecharObservacoesMedicao();">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal inmodal animated fadeIn" id="ModalObservacao" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-editar-header">
                                    <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                                    <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacao</span>
                                </div>
                                <div class="modal-body">

                                    <div id="Observacao_resultado" min-height:500px;">
                                        @Html.Partial("_ObservacaoMedicao")
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>

                                    @if (!isUser.isOperador(IDTipoAcesso) && IDTipoAcesso != 10)
                                    {
                                        <button type="button" class="btn btn-primary" onclick="SalvarObservacaoMedicao();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }

<script type="text/javascript">
    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-medicoes').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "numero" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "numero", "bSearchable": false, },
                        { "aTargets": [7], "sType": "numero", "bSearchable": false, },
                        { "aTargets": [8], "sType": "numero", "bVisible": false, "bSortable": true, "bSearchable": false },
                        { "aTargets": [9], "sType": "portugues", "bSearchable": false },
                        { "aTargets": [10], "sType": "portugues" },
                        { "aTargets": [11], "bVisible": true, "bSortable": false, "bSearchable": false },
                        { "aTargets": [12], "sType": "numero", "bVisible": false, "bSortable": true, "bSearchable": false },
            ],
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "3%" },
            { sWidth: "16%" },
            { sWidth: "8%" },
            { sWidth: "8%" },
            { sWidth: "8%" },
            { sWidth: "1%" },
            { sWidth: "10%" },
            { sWidth: "10%" },
            { sWidth: "10%" },
            { sWidth: "1%" },
            ],
            "order": [[12, "desc"], [8, "desc"]],

            "rowCallback": function( row, data, index ) {
                if (parseInt(data[10]) > 0)
                {
                    $('td', row).css('color', 'Red');
                    $('td', row).css('font-weight', 'bold');
                }
                else 
                {
                    $('td', row).css('color', 'Black');
                    $('td', row).css('font-weight', 'normal');
                }
            },

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });
    });

    function ObservacoesMedicao(e, IDMedicao) {

        e.stopPropagation();

        // apresenta modal
        $("#ModalObservacoes").modal("show");

        // aguarde
        $('#Observacoes_resultado').css("display", "none");

        // IDMedicao
        document.getElementById("IDMedSelecionado").value = IDMedicao;

        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/_ObservacoesMedicao',
            dataType: 'html',
            data: { "IDMedicao": IDMedicao },
            cache: false,
            async: true,
            success: function (data) {

                $('#Observacoes_resultado').css("display", "block");
                $('#Observacoes_resultado').html(data);
            }
        });
    }

    function FecharObservacoesMedicao() {

        // fecha modal
        $("#ModalObservacoes").modal("hide");

        // atualiza pagina
        location.reload();
    }

    function ObservacaoMedicao(e, IDMedicao, IDObservacao) {

        e.stopPropagation();

        // apresenta modal
        $("#ModalObservacao").modal("show");

        // aguarde
        $('#Observacao_resultado').css("display", "none");

        // IDMedicao e IDObservacao
        if (IDMedicao == 0) {
            // IDMedicao
            IDMedicao = document.getElementById("IDMedSelecionado").value;
        }
        else {
            document.getElementById("IDMedSelecionado").value = IDMedicao;
        }

        document.getElementById("IDObservacaoSelecionado").value = IDObservacao;

        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/_ObservacaoMedicao',
            dataType: 'html',
            data: { "IDMedicao": IDMedicao, "IDObservacao": IDObservacao },
            cache: false,
            async: true,
            success: function (data) {

                $('#Observacao_resultado').css("display", "block");
                $('#Observacao_resultado').html(data);
            }
        });
    }

    function SalvarObservacaoMedicao() {

        // IDObservacao
        var IDObservacao = document.getElementById("IDObservacaoSelecionado").value;

        // IDMedicao
        var IDMedicao = document.getElementById("IDMedSelecionado").value;

        // IDTag
        var IDTag = $("#tags").val();

        // data
        var data_relat = document.querySelector('[name="data_relat"]').value;

        // cliente visualiza
        var ClienteVisualizaObj = document.getElementsByName('ClienteVisualiza');
        var ClienteVisualiza = ClienteVisualizaObj[0].checked;

        // Observacao
        var ObservacaoTexto = document.getElementById("ObservacaoTexto").value;

        // salvar
        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/ObservacaoMedicao_Salvar',
            data: { 'IDObservacao': IDObservacao, 'IDMedicao': IDMedicao, 'IDTag': IDTag, 'ClienteVisualiza': ClienteVisualiza, 'ObservacaoData': data_relat, 'ObservacaoTexto': ObservacaoTexto },
            contentType: 'application/html',
            dataType: 'html',
            cache: false,
            async: true,
            success: function (data) {

                setTimeout(function () {

                    $.ajax(
                    {
                        type: 'GET',
                        url: '/Supervisao/_ObservacoesMedicao',
                        dataType: 'html',
                        data: { "IDMedicao": IDMedicao },
                        cache: false,
                        async: true,
                        success: function (data) {

                            $('#Observacoes_resultado').css("display", "block");
                            $('#Observacoes_resultado').html(data);
                        }
                    });

                }, 100);
            },
            error: function (response) {

            }
        });
    }

    function ResolvidoObservacaoMedicao(IDObservacao) {
        event.stopPropagation();

        // IDMedicao
        var IDMedicao = document.getElementById("IDMedSelecionado").value;

        // resolvido
        $.ajax(
        {
            type: 'GET',
            url: '/Supervisao/ObservacaoMedicao_Resolvido',
            data: { 'IDObservacao': IDObservacao },
            contentType: 'application/html',
            dataType: 'html',
            cache: false,
            async: true,
            success: function (data) {

                setTimeout(function () {

                    $.ajax(
                    {
                        type: 'GET',
                        url: '/Supervisao/_ObservacoesMedicao',
                        dataType: 'html',
                        data: { "IDMedicao": IDMedicao },
                        cache: false,
                        async: true,
                        success: function (data) {

                            $('#Observacoes_resultado').css("display", "block");
                            $('#Observacoes_resultado').html(data);
                        }
                    });

                }, 100);
            },
            error: function (response) {

            }
        });
    };

    function ExcluirObservacaoMedicao(IDObservacao, DataTexto) {
        event.stopPropagation();

        // IDMedicao
        var IDMedicao = document.getElementById("IDMedSelecionado").value;

        // titulo
        titulo = "Deseja excluir a Observação?<br/>" + DataTexto;

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // excluir
            $.ajax(
            {
                type: 'GET',
                url: '/Supervisao/ObservacaoMedicao_Excluir',
                data: { 'IDObservacao': IDObservacao },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Supervisao/_ObservacoesMedicao',
                            dataType: 'html',
                            data: { "IDMedicao": IDMedicao },
                            cache: false,
                            async: true,
                            success: function (data) {

                                $('#Observacoes_resultado').css("display", "block");
                                $('#Observacoes_resultado').html(data);
                            }
                        });

                    }, 100);
                },
                error: function (response) {

                }
            });
        });
    };

</script>

}


