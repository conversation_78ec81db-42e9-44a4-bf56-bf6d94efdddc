﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

<div class="row">
    <div class="col-lg-12">

        <div class="panel panel-info">
            <div class="panel-body">

                @{
                    bool GatewayConectada_IoT = ViewBag.GatewayConectada_IoT;
                    int IDTipoAcesso = ViewBag._IDTipoAcesso;
                    int IDGateway = ViewBag._IDGateway;

                    // caso Demo
                    if (IDTipoAcesso == TIPO_ACESSO.DEMONSTRACAO)
                    {
                        GatewayConectada_IoT = true;
                    }

                    if (GatewayConectada_IoT)
                    {
                        <table class="table table-striped table-bordered table-hover dataTables-entradas">
                            <thead>
                                <tr>
                                    <th>@SmartEnergy.Resources.MenuTexts.MenuLateralConfiguracaoED</th>
                                    <th>@SmartEnergy.Resources.SupervisaoTexts.NumEntrada</th>
                                    <th>@SmartEnergy.Resources.SupervisaoTexts.Entrada</th>
                                    <th>@SmartEnergy.Resources.SupervisaoTexts.Estado</th>
                                </tr>
                            </thead>
                            <tbody>

                                @{
                                    List<EntradasDigitaisDominio> entradas = ViewBag.listaEntradas;

                                    var classe = "badge-secondary";
                                    string descricao = "Desconhecido";
                                    string cor_led = "led-gray";

                                    if (entradas != null)
                                    {
                                        foreach (EntradasDigitaisDominio entrada in entradas)
                                        {
                                            <tr>
                                                <td>@entrada.Descricao</td>
                                                <td>@entrada.NumEntradaGateway</td>

                                                @{
                                                    bool piscar = false;

                                                    switch (entrada.status)
                                                    {
                                                        case STATUS_ED.DESCONHECIDO:

                                                            classe = "badge-secondary";
                                                            descricao = "Desconhecido";
                                                            cor_led = "led-gray";
                                                            break;

                                                        case STATUS_ED.TIMEOUT_GATEWAY:

                                                            classe = "badge-warning";
                                                            descricao = "Erro de Timeout [Gateway]";
                                                            piscar = true;
                                                            cor_led = "led-yellow";
                                                            break;

                                                        case STATUS_ED.TIMEOUT_MQTT:

                                                            classe = "badge-warning";
                                                            descricao = "Erro de Timeout [MQTT]";
                                                            piscar = true;
                                                            cor_led = "led-yellow";
                                                            break;

                                                        case STATUS_ED.SOLICITANDO:

                                                            classe = "badge-warning";
                                                            descricao = "Solicitando";
                                                            cor_led = "led-yellow";
                                                            break;

                                                        case STATUS_ED.INATIVO:

                                                            classe = "badge-danger";
                                                            descricao = entrada.Desativou;
                                                            cor_led = "led-red";
                                                            break;

                                                        case STATUS_ED.ATIVO:

                                                            classe = "badge-primary";
                                                            descricao = entrada.Ativou;
                                                            cor_led = "led-green";
                                                            break;

                                                    }

                                                    if (piscar)
                                                    {
                                                        <td><blink><div class="@cor_led"></div></blink></td>
                                                    }
                                                    else
                                                    {
                                                        <td><div class="@cor_led"></div></td>
                                                    }

                                                    <td><span class="badge @classe" style="width:300px;">&nbsp;&nbsp;@descricao&nbsp;&nbsp;</span></td>
                                                }
                                            </tr>
                                        }
                                    }
                                }

                            </tbody>
                        </table>
                    }
                    else
                    {
                        <br /><br /><br /><br /><br /><br />
                        <div class="row">
                            <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                <i class="fa fa-chain-broken fa-5x" style="color:red"></i>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                <h2 style="color:red">Gateway não conectada no Servidor IoT</h2>
                            </div>
                        </div>
                        <br /><br /><br /><br /><br /><br />
                    }
                }

            </div>

            <div class="overlay_aguarde" style="display: none">
                <div class="spinner-aguarde">
                    <div class="sk-spinner sk-spinner-wandering-cubes">
                        <div class="sk-spinner sk-spinner-wave">
                            <div class="sk-rect1 text-ligh"></div>
                            <div class="sk-rect2"></div>
                            <div class="sk-rect3"></div>
                            <div class="sk-rect4"></div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-entradas').DataTable({
            "iDisplayLength": 12,
            dom: 'ftp',
            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "numero" },
                        { "aTargets": [2], "bSortable": false, "bSearchable": false },
                        { "aTargets": [3], "sType": "portugues" },
            ],
            "aoColumns": [
                { sWidth: "53%" },
                { sWidth: "20%" },
                { sWidth: "2%" },
                { sWidth: "20%" },
            ],
            'order': [1, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

    });

</script>
