﻿@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoMedicao;

    int IDConsultor = ViewBag._IDConsultor;
}

<div class="row wrapper border-bottom white-bg page-heading">
    <div class="row">
        <div class="col-lg-12">
            <div class="superv-content">

                <div class="col-lg-3 col-md-12">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-util">
                                    <h4>@ViewBag.NomeGrandeza</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.MaximoDia</h5>
                                    <h1>@ViewBag.DiaAtual_Valor_Max <span>@ViewBag.UnidadeGrandeza</span></h1>
                                    <h6>@ViewBag.DiaAtual_MaxDataHora</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-util">
                                    <h4>@ViewBag.NomeGrandeza</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.MedioDia</h5>
                                    <h1>@ViewBag.DiaAtual_Valor_Medio <span>@ViewBag.UnidadeGrandeza</span></h1>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela superv-util">
                                <table class="table no-margins" style="width:100%;">
                                    <thead>
                                        <tr>
                                            <th style="width:34%;"></th>
                                            <th style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.Maximo</th>
                                            <th style="width:33%;">@SmartEnergy.Resources.SupervisaoTexts.Medio</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAtual<br /><small>(@ViewBag.MesAtual_Data)</small></td>
                                            <td>@ViewBag.MesAtual_Valor_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.MesAtual_MaxDataHora)</small></td>
                                            <td>@ViewBag.MesAtual_Valor_Medio @ViewBag.UnidadeGrandeza</td>
                                        </tr>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAnterior<br /><small>(@ViewBag.MesAnt1_Data)</small></td>
                                            <td>@ViewBag.MesAnt1_Valor_Max @ViewBag.UnidadeGrandeza<br /><small>(@ViewBag.MesAnt1_MaxDataHora)</small></td>
                                            <td>@ViewBag.MesAnt1_Valor_Medio @ViewBag.UnidadeGrandeza</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-md-12">
                    <div class="row">
                        <div class="superv-grafico">
                            <div class="flot-chart">
                                <div id="utilidades-chart"></div>
                            </div>
                            <div class='chart-legend'>
                                <div class='legend-scale-dem'>
                                    <ul class='legend-labels'>
                                        <li><span style='background:#55A3D4;'></span>@ViewBag.NomeGrandeza</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-8">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual superv-util">
                                    <h4>@ViewBag.NomeGrandeza</h4>
                                    <h5>@SmartEnergy.Resources.DashboardTexts.TotalDia</h5>
                                    <h1>@ViewBag.DiaAtual_Valor_Total <span>@ViewBag.UnidadeGrandeza</span></h1>
                                    <h6>@ViewBag.DiaAtual_Data</h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-tabela superv-util">
                                <table class="table no-margins">
                                    <thead>
                                        <tr>
                                            <th style="width: 50%;"></th>
                                            <th style="width: 50%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAtual<br /><small>(@ViewBag.MesAtual_Data)</small></td>
                                            <td>@ViewBag.MesAtual_Valor_Total @ViewBag.UnidadeGrandeza<br /><small>&nbsp;</small></td>
                                        </tr>
                                        <tr>
                                            <td>@SmartEnergy.Resources.SupervisaoTexts.MesAnterior<br /><small>(@ViewBag.MesAnterior)</small></td>
                                            <td>@ViewBag.MesAnt1_Valor_Total @ViewBag.UnidadeGrandeza<br /><small>&nbsp;</small></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="superv-coluna">
                                <div class="superv-valorAtual">

                                    @{
                                        if (ViewBag.Valor_Total_PorcAntN > 0)
                                        {
                                            <h5 class="superv-ponta"><blink><i class="fa fa-arrow-circle-up" style="font-size:20px;"></i></blink><span style="font-weight: bold; padding-left:8px;">O Total aumentou @ViewBag.Valor_Total_PorcAnt em relação ao mês anterior.</span></h5>
                                        }
                                        else
                                        {
                                            <h5 class="superv-fponta"><i class="fa fa-arrow-circle-down" style="font-size:20px;"></i><span style="font-weight: bold; padding-left:8px;">O Total reduziu @ViewBag.Valor_Total_PorcAnt em relação ao mês anterior.</span></h5>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<div class="wrapper wrapper-content">


    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-body">

                    <div class="col-lg-3">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="superv-coluna">
                                    <div class="superv-valorAtual superv-util">
                                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Ciclometro</h4>
                                        <br />
                                        <h5>@SmartEnergy.Resources.SupervisaoTexts.Inicio</h5>
                                        <h1>@ViewBag.CicloIni_DiaAtual_Valor</h1>
                                        <h6>@ViewBag.CicloIni_DiaAtual_Data</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-3">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="superv-coluna">
                                    <div class="superv-valorAtual superv-util">
                                        <h4>&nbsp;</h4>
                                        <br />
                                        <h5>@SmartEnergy.Resources.SupervisaoTexts.Fim</h5>
                                        <h1>@ViewBag.CicloFim_DiaAtual_Valor</h1>
                                        <h6>@ViewBag.CicloFim_DiaAtual_Data</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="superv-tabela">
                                    <table class="table no-margins superv-util" style="width:100%;">
                                        <thead>
                                            <tr>
                                                <th style="width:40%;"></th>
                                                <th style="width:30%;">@SmartEnergy.Resources.SupervisaoTexts.Inicio</th>
                                                <th style="width:30%;">@SmartEnergy.Resources.SupervisaoTexts.Fim</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>@SmartEnergy.Resources.SupervisaoTexts.MesAtual<br /><small>(@ViewBag.MesAtual_Data)</small></td>
                                                <td>@ViewBag.CicloIni_MesAtual_Valor<br /><small>@ViewBag.CicloIni_MesAtual_Data</small></td>
                                                <td>@ViewBag.CicloFim_MesAtual_Valor<br /><small>@ViewBag.CicloFim_MesAtual_Data</small></td>
                                            </tr>
                                            <tr>
                                                <td>@SmartEnergy.Resources.SupervisaoTexts.MesAnterior<br /><small>(@ViewBag.MesAnterior)</small></td>
                                                <td>@ViewBag.CicloIni_MesAnt1_Valor<br /><small>@ViewBag.CicloIni_MesAnt1_Data</small></td>
                                                <td>@ViewBag.CicloFim_MesAnt1_Valor<br /><small>@ViewBag.CicloFim_MesAnt1_Data</small></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>


    @{
        if (ViewBag.IDAgenteDistribuidora > 0)
        {
            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-title">
                        <div class="panel-body">

                            <div class="col-lg-6 col-md-12">
                                <div class="row">
                                    <div class="superv-grafico-fatura">
                                        <div class="flot-chart">
                                            <div id="fatura-chart"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="superv-coluna">
                                            <div class="superv-valorAtual superv-util">
                                                <h5>@SmartEnergy.Resources.DashboardTexts.FaturaAtual</h5>
                                                <h1>@ViewBag.Fatura_Atual_ValorTotal</h1>
                                                <h6>@ViewBag.Fatura_Atual_Data</h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="superv-tabela">
                                            <table class="table no-margins superv-util" style="width:100%;">
                                                <thead>
                                                    <tr>
                                                        <th style="width:30%;"></th>
                                                        <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.Total</th>
                                                        <th style="width:15%;"></th>
                                                        <th style="width:25%;">@SmartEnergy.Resources.SupervisaoTexts.CustoMedio</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>@ViewBag.Fatura_Atual_Data</td>
                                                        <td>@ViewBag.Fatura_Atual_ValorTotal</td>

                                                        @if (@ViewBag.Fatura_PorcAtualAnt1N > 0.0)
                                                        {
                                                            <td style='color:#ff0000'><i class='fa fa-level-up'></i> @ViewBag.Fatura_PorcAtualAnt1</td>
                                                        }
                                                        else
                                                        {
                                                            <td style='color:#007f00'><i class='fa fa-level-down'></i> @ViewBag.Fatura_PorcAtualAnt1</td>
                                                        }

                                                        <td>@ViewBag.Fatura_Atual_CustoMedio</td>
                                                    </tr>
                                                    <tr class="superv-energia">
                                                        <td>@ViewBag.Fatura_Anterior1_Data</td>
                                                        <td>@ViewBag.Fatura_Anterior1_ValorTotal</td>

                                                        @if (@ViewBag.Fatura_PorcAnt1Ant2N > 0.0)
                                                        {
                                                            <td style='color:#ff0000'><i class='fa fa-level-up'></i> @ViewBag.Fatura_PorcAnt1Ant2</td>
                                                        }
                                                        else
                                                        {
                                                            <td style='color:#007f00'><i class='fa fa-level-down'></i> @ViewBag.Fatura_PorcAnt1Ant2</td>
                                                        }

                                                        <td>@ViewBag.Fatura_Anterior1_CustoMedio</td>
                                                    </tr>
                                                    <tr class="superv-energia">
                                                        <td>@ViewBag.Fatura_Anterior2_Data</td>
                                                        <td>@ViewBag.Fatura_Anterior2_ValorTotal</td>
                                                        <td></td>
                                                        <td>@ViewBag.Fatura_Anterior2_CustoMedio</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    }

    @{
        // RETHA nao quer que aparece GATEWAY
        if (IDConsultor != 4749)
        {

            <div class="row">
                <div class="col-lg-3">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>Gateway</h4><br />

                            @{
                                int IDTipoAcesso = ViewBag._IDTipoAcesso;

                                if (IDTipoAcesso == 0 || IDTipoAcesso == 5)
                                {
                                    <span>[@ViewBag._IDGateway] </span>
                                }
                            }
                            @ViewBag.GatewayNome
                        </div>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.ModeloVersao</h4><br />
                            @ViewBag.GatewayModelo
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>Status</h4><br />
                            @ViewBag.GatewayStatus
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</h4><br />
                            @ViewBag.GatewayAtualizacao
                        </div>
                    </div>
                </div>
                <div class="col-lg-2">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4>@SmartEnergy.Resources.SupervisaoTexts.DataHoraEq</h4><br />
                            @ViewBag.GatewayDataEq
                        </div>
                    </div>
                </div>
            </div>

        }
    }        
        
    @if (@ViewBag.listaErros.Count > 0)
    {
        <div class="row">
            <div class="col-lg-12">
                <div class="panel panel-danger">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.SupervisaoTexts.Erros</h4><br />
                        <ul style="list-style-type:disc; margin-left: 20px;">
                            @foreach (var erro in @ViewBag.listaErros)
                            {
                                <li>@erro</li>
                            }
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    }

</div>

@section Styles {
    @Styles.Render("~/plugins/c3Styles")
    @Styles.Render("~/plugins/select2Styles")
    @Styles.Render("~/Supervisao.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/select2")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">
        $(document).ready(function () {

            // utilidades
            var dataX = [];
            var labelX = [];
            var dataValor = [];
            var dataValor_CicloIni = [];
            var dataValor_CicloFim = [];

            // copia valores
            var Valor = @Html.Raw(Json.Encode(@ViewBag.Valor));
            var Dias = @Html.Raw(Json.Encode(@ViewBag.Dias));
            var Valor_CicloIni = @Html.Raw(Json.Encode(@ViewBag.Valor_CicloIni));
            var Hora_CicloIni = @Html.Raw(Json.Encode(@ViewBag.Hora_CicloIni));
            var Valor_CicloFim = @Html.Raw(Json.Encode(@ViewBag.Valor_CicloFim));
            var Hora_CicloFim = @Html.Raw(Json.Encode(@ViewBag.Hora_CicloFim));
            var NomeGrandeza = @Html.Raw(Json.Encode(@ViewBag.NomeGrandeza));
            var UnidadeGrandeza = @Html.Raw(Json.Encode(@ViewBag.UnidadeGrandeza));

            var minimoValor = Math.ceil(@ViewBag.Valor_min);
            var maximoValor = Math.ceil(@ViewBag.Valor_max);

            dataX.push('x');
            dataValor.push(['data1']);

            for (i = 0; i < 26; i++)
            {
                // X
                dataX.push(Dias[i]);

                dataValor.push([Valor[i]]);
            }

            // valores label X
            labelX.push(Dias[1]);
            for (i = 3; i <= 24; i+=3)
            {
                labelX.push(Dias[i]);
            }

            var Data = [dataX,dataValor];

            c3.generate({
                bindto: '#utilidades-chart',
                size: {
                    height: 300
                },
                padding: {
                    top: 8,
                    right: 10,
                    bottom: 30,
                    left: 43
                },
                data: {
                    x: 'x',
                    xFormat : '%Y-%m-%d %H:%M:%S',
                    columns: Data,
                    type:'bar',
                    colors: {
                        data1: '#1C84C6'
                    }
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                legend: {
                    show: false
                },
                axis: {
                    x:  {
                        type: 'timeseries',
                        tick: {
                            outer: false,
                            values: labelX,
                            format: function (x) {

                                // horas e minutos
                                var hours = x.getHours();
                                var minutes = x.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                var strTime = hours + ':' + minutes;

                                // verifica se nao deve mostrar label
                                if(hours==3 || hours==9 || hours==15 || hours==21)
                                    return "";

                                // retorna hora e minuto
                                return strTime;
                            }
                        },
                        // Grafico 15minutos = -(1000 milisegundos)*(60 segundos)*(6 minutos)
                        // Grafico 1 hora    = -(1000 milisegundos)*(60 segundos)*(28 minutos)
                        padding: -1000*60*28
                    },
                    y:  {
                        min: minimoValor,
                        max: maximoValor,
                        padding: {
                            top: 0,
                            bottom: 0
                        },
                        tick: {
                            outer: false,
                            format: function (d) {
                                return parseInt(d);
                            }
                        }
                    }
                },
                grid: {
                    x: {
                        show: false
                    }
                },
                bar: {
                    width: { ratio: 0.25 },
                },
                tooltip: {
                    format: {
                        /*...*/
                    },
                    contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                        var $$ = this, config = $$.config,
                            titleFormat = config.tooltip_format_title || defaultTitleFormat,
                            nameFormat = config.tooltip_format_name || function (name) { return name; },
                            valueFormat = config.tooltip_format_value || defaultValueFormat,
                            text, i, title, value, name, bgcolor;
                        for (i = 0; i < d.length; i++) {
                            if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                            if (! text) {

                                // split string and create array.
                                var arr = Dias[d[i].index].split(/-|\s|:/);

                                // decrease month value by 1
                                var data = new Date(arr[0], arr[1] -1, arr[2], arr[3], arr[4], arr[5]);

                                // horas e minutos
                                var hours = data.getHours();
                                var minutes = data.getMinutes();

                                hours = hours < 10 ? '0'+hours : hours;
                                minutes = minutes < 10 ? '0'+minutes : minutes;
                                title = hours + ':' + minutes;

                                text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                            }

                            name = NomeGrandeza;
                            value = d[i].value;
                            bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";

                            if( value < 0 )
                            {
                                text += "<td class='value'> -.-- " + UnidadeGrandeza + "</td>";
                            }
                            else
                            {
                                text += "<td class='value'>" + value.toFixed(2) + " " + UnidadeGrandeza + "</td>";
                            }

                            text += "</tr>";
                        }

                        // indice
                        var j = d[0].index;

                        text += "<tr>";
                        text += "<td class='name'>Ciclômetro<br>Início<br>&nbsp;<br><br>Fim<br>&nbsp;" + "</td>";
                        text += "<td class='value'>&nbsp;<br>" + Valor_CicloIni[j] + "<br>" + Hora_CicloIni[j] + "<br><br>" + Valor_CicloFim[j] + "<br>" + Hora_CicloFim[j] + "</td>";
                        text += "</tr>";

                        return text + "</table>";
                    }
                }
            });



            // fatura

            // copia valores
            var Fatura_Atual_Data = @Html.Raw(Json.Encode(ViewBag.Fatura_Atual_DataMes));
            var Fatura_Atual_ValorTotal = @Html.Raw(Json.Encode(ViewBag.Fatura_Atual_ValorTotalN));

            var Fatura_Anterior1_Data = @Html.Raw(Json.Encode(ViewBag.Fatura_Anterior1_DataMes));
            var Fatura_Anterior1_ValorTotal = @Html.Raw(Json.Encode(ViewBag.Fatura_Anterior1_ValorTotalN));

            var Fatura_Anterior2_Data = @Html.Raw(Json.Encode(ViewBag.Fatura_Anterior2_DataMes));
            var Fatura_Anterior2_ValorTotal = @Html.Raw(Json.Encode(ViewBag.Fatura_Anterior2_ValorTotalN));

            var x = ['x', Fatura_Atual_Data, Fatura_Anterior1_Data, Fatura_Anterior2_Data];
            var Data = ['data1',Fatura_Atual_ValorTotal, Fatura_Anterior1_ValorTotal, Fatura_Anterior2_ValorTotal];

            var maxValor = Fatura_Atual_ValorTotal;

            if( Fatura_Anterior1_ValorTotal > maxValor)
            {
                maxValor = Fatura_Anterior1_ValorTotal;
            }

            if( Fatura_Anterior2_ValorTotal > maxValor)
            {
                maxValor = Fatura_Anterior2_ValorTotal;
            }

            maxValor = maxValor * 1.1;


            c3.generate({
                bindto: '#fatura-chart',
                size: {
                    height: 230
                },
                padding: {
                    top: 8,
                    right: 0,
                    bottom: 20,
                    left: 60
                },
                data: {
                    x: 'x',
                    columns: [
                        x,
                        Data
                    ],
                    type: 'bar',
                    labels: {
                        format: {
                            data1: function (v, id, i, j) {

                                var valor = v * 100;
                                var str_valor = valor.toFixed(0);
                                return "R$ " + formatReal( str_valor );
                            },
                        }
                    },
                    color: function(inColor, data) {
                        var colors = ['#1C84C6', '#1AB394', '#1AB394'];
                        if(data.index !== undefined) {
                            return colors[data.index];
                        }

                        return inColor;
                    }
                },
                point: {
                    show: false,
                    sensitivity: 1000
                },
                legend: {
                    show: false
                },
                axis:{
                    rotated: true,
                    x : {
                        type : 'categories',
                        tick: {
                            fit: true
                        }
                    },
                    y : {
                        show: false,
                        max: maxValor
                    }
                },
                tooltip: {
                    format: {
                        /*...*/
                    },
                    contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                        var $$ = this, config = $$.config,
                            titleFormat = config.tooltip_format_title || defaultTitleFormat,
                            nameFormat = config.tooltip_format_name || function (name) { return name; },
                            valueFormat = config.tooltip_format_value || defaultValueFormat,
                            text, i, title, value, name, bgcolor;
                        for (i = 0; i < d.length; i++) {
                            if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                            if (! text) {
                                title = titleFormat ? titleFormat(d[i].x) : d[i].x;
                                text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2' style='background-color:#1C84C6'>" + title + "</th></tr>" : "");
                            }

                            name = "Total";

                            value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);

                            var valor = d[i].value * 100;
                            var str_valor = valor.toFixed(0);
                            value = "R$ " + formatReal( str_valor );


                            bgcolor = (d[i].index == 0) ? '#1C84C6' : '#1AB394';

                            text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                            text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "</td>";
                            text += "<td class='value'>" + value + "</td>";
                            text += "</tr>";
                        }
                        return text + "</table>";
                    }
                }
            });

            piscando();
        });

        function piscando() {

            var tempo = 500; //1000 = 1s

            blinks = document.getElementsByTagName("blink");
            for(var i=0;i<blinks.length;i++){
                if(blinks[i].getAttribute("style")=="VISIBILITY: hidden"){
                    blinks[i].setAttribute("style", "VISIBILITY: visible");
                }else{
                    blinks[i].setAttribute("style", "VISIBILITY: hidden");
                }
            }
            setTimeout('piscando()', tempo);
        }

        function formatReal( int )
        {
            var tmp = int+'';
            tmp = tmp.replace(/([0-9]{2})$/g, ",$1");
            if( tmp.length > 6 )
                tmp = tmp.replace(/([0-9]{3}),([0-9]{2}$)/g, ".$1,$2");

            return tmp;
        }

    </script>
}





