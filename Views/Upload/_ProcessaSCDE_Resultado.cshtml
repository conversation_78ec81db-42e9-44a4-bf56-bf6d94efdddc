﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    List<PROCESSA_SCDE_RESULTADO> processaSCDE_resultado = ViewBag.processaSCDE_resultado;
}

<div class="row">
    <div class="col-lg-12">

        <div class="panel panel-info">

            <div class="panel-body">

                <table id="dataTables-scde" class="table table-striped table-bordered table-hover dataTables-scde">
                    <thead>
                        <tr>
                            <th>Arquivo</th>
                            <th>Status</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.Agente</th>
                            <th>@SmartEnergy.Resources.ConfiguracaoTexts.PontoMedicao</th>
                            <th>Início</th>
                            <th>Fim</th>
                            <th>IDMedicao</th>
                            <th>Número de Registros</th>
                        </tr>
                    </thead>
                    <tbody>

                        @{
                            if (processaSCDE_resultado != null)
                            {
                                foreach (PROCESSA_SCDE_RESULTADO result in processaSCDE_resultado)
                                {
                                    <tr>
                                        <td style="font-weight:900;">@result.NomeArquivo</td>

                                        @{
                                            string nome_status = "";
                                            string linha_status = "";
                                            
                                            
                                            if (result.status == PROCESSA_SCDE_STATUS.PROCESSO_OK)
                                            {
                                                nome_status = "Processado OK";
                                                
                                                if( result.linha_inicio == result.linha_fim )
                                                {
                                                    linha_status = string.Format("(Linha {0})", result.linha_inicio);
                                                }
                                                else
                                                {
                                                    linha_status = string.Format("(Linha {0}-{1})", result.linha_inicio, result.linha_fim);
                                                }
                                                
                                                <td>@nome_status</td>
                                                <td>@result.agente</td>
                                                <td>@result.ponto_grupo</td>
                                                <td>@string.Format("{0:dd/MM/yyyy HH:mm}", result.inicio)</td>
                                                <td>@string.Format("{0:dd/MM/yyyy HH:mm}", result.fim)</td>
                                                <td>@result.IDMedicao</td>
                                                <td>@result.NumRegistros</td>
                                            }
                                            else
                                            {
                                               
                                                switch(result.status)
                                                {
                                                    default:
                                                        nome_status = result.status.ToString();
                                                        break;

                                                    case PROCESSA_SCDE_STATUS.ARQ_INEX:
                                                        nome_status = "Arquivo Inexistente";
                                                        break;

                                                    case PROCESSA_SCDE_STATUS.SEM_REG:
                                                        nome_status = "Sem registros no arquivo";
                                                        break;

                                                    case PROCESSA_SCDE_STATUS.NUM_COL_ERRADO:
                                                        nome_status = "Quantidade de colunas incorretas";
                                                        linha_status = string.Format("(Linha {0})", result.linha_inicio);
                                                        break;

                                                    case PROCESSA_SCDE_STATUS.MED_INEX:
                                                        nome_status = "Medição inexistente";
                                                        linha_status = string.Format("(Linha {0})", result.linha_inicio);
                                                        break;

                                                    case PROCESSA_SCDE_STATUS.PONTO_GRUPO_VAZIO:
                                                        nome_status = "Ponto/Grupo incorreto";
                                                        linha_status = string.Format("(Linha {0})", result.linha_inicio);
                                                        break;
                                                        
                                                    case PROCESSA_SCDE_STATUS.DATA_HORA_INCORRETA:
                                                        nome_status = "Data e Hora incorreta";
                                                        linha_status = string.Format("(Linha {0})", result.linha_inicio);
                                                        break;
                                                        
                                                    case PROCESSA_SCDE_STATUS.CONSUMO_INCORRETO:
                                                        nome_status = "Consumo incorreto";
                                                        linha_status = string.Format("(Linha {0})", result.linha_inicio);
                                                        break;

                                                    case PROCESSA_SCDE_STATUS.ERRO_SALVAR_REG:
                                                        nome_status = "Erro ao salvar registro";
                                                        linha_status = string.Format("(Linha {0})", result.linha_inicio);
                                                        break;
                                                }
                                                
                                                <td>@nome_status<br />@linha_status</td>
                                            
                                                if( string.IsNullOrEmpty(result.agente) )
                                                {
                                                    <td>---</td>
                                                }
                                                else
                                                {
                                                    <td>@result.agente</td>
                                                }

                                                if (string.IsNullOrEmpty(result.ponto_grupo))
                                                {
                                                    <td>---</td>
                                                }
                                                else
                                                {
                                                    <td>@result.ponto_grupo</td>
                                                }
                                                
                                                <td>---</td>
                                                <td>---</td>
                                                <td>---</td>
                                                <td>---</td>
                                            }
                                        }

                                    </tr>
                                }
                            }
                        }

                    </tbody>
                </table>

            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "---") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-scde').DataTable({
            "bAutoWidth": false,
            "iDisplayLength": 10,
            dom: 'tp',
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "portugues" },
                        { "aTargets": [7], "sType": "portugues" },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "aoColumns": [
            { sWidth: "16%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
