﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes
@using System.Globalization

@{
    ViewBag.Title = "Consumos de Utilidades Mensal" ;
}

<head>
    <title>Consumos de Utilidades Mensal</title>
    <style>

        .link_branco a:link {
            color: #ffffff !important;
        }
        .link_branco a:visited {
            color: #ffffff  !important;
        }
        .link_branco a:hover {
            color: #f0f0f0  !important;
        }
        .link_branco a:active {
            color: #ffffff  !important;
        }

    </style>
</head>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Upload_UtilidadesMensal", "Upload", FormMethod.Post, new { id = "form", role = "form" }))
            {

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>Enviar Consumos de Utilidades Mensal</h4>
                    </div>

                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-3">
                                <label class="control-label">&nbsp;Consumos de Utilidades Mensal (.XLS)</label><br />
                                <input class="form-control" disabled="disabled" id="nomeHistorico" name="nomeHistorico" type="text" value="" />
                            </div>
                            <div class="form-group col-lg-4">
                                <div id="progress_Historico" class="progress">
                                    <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                </div>
                                <span id="BotaoUpload" class="btn btn-primary fileinput-button link_branco" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">

                                    @{
                                        int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                        int IDConsultor = ViewBag._IDConsultor;

                                        if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDConsultor == CLIENTES_ESPECIAIS.BRMALLS)
                                        {
                                            <span>Selecione a planilha com os Consumos de Utilidades Mensal</span>
                                            <input type="file" id="fileupload_Historico" name="fileupload_Historico">
                                        }
                                        else
                                        {
                                            <a data-toggle="modal" href="#modalComercial"><span>Selecione a planilha com os Consumos de Utilidades Mensal</span></a>
                                        }
                                    }

                                </span>
                            </div>

                            <div class="modal inmodal animated fadeIn" id="modalComercial" tabindex="-1" role="dialog" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                                            <h4 class="modal-title"><i class="fa fa-upload"></i>&nbsp;&nbsp;Consumos de Utilidades Mensal</h4>
                                        </div>
                                        <div class="modal-body">
                                            <br /><p><label>Através deste recurso é possível executar a carga dos valores de Consumo de Utilidades Mensal.</label></p>
                                            <br /><p><label>Entre em contato com o departamento Comercial Smart Energy para obter o acesso a este recurso.</label></p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                                        </div>
                                    </div>
                                </div>
                            </div>




                            <div class="form-group col-lg-offset-1 col-lg-4">
                                <button type="button" class="btn btn-info btn-lg pull-right" onclick="DownloadModelo();" style="color:#ffffff; width:100%;">
                                    <span style="font-size:large">Modelo de planilha de Consumos de Utilidades Mensal</span>
                                </button>
                            </div>
                            <br /><br /><br />
                        </div>

                        <div class="row">
                            <div id="div_selecione" class="form-group col-lg-12" style="display:block; height:550px; padding-top:60px;">
                                <div class="row">
                                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                        <i class="fa fa-upload fa-5x"></i>
                                    </div>
                                </div>
                                <br />
                                <div class="row">
                                    <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                        <h2>Selecionar a planilha com os Consumos de Utilidades Mensal</h2>
                                    </div>
                                </div>
                            </div>

                            <div id="div_aguarde" class="form-group col-lg-12" style="display:none; height:550px; padding-top:60px;">

                                <div class="row">
                                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                        <i class="fa fa-gears fa-5x"></i>
                                    </div>
                                </div>
                                <br />
                                <div class="row">
                                    <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                        <h2>Processando Consumos de Utilidades Mensal</h2>
                                    </div>
                                </div>
                                <br />
                                <br />

                                <div class="row">
                                    <div class="col-lg-12">
                                        <label id="medicao_atual" class="control-label">[0/0] ---</label>
                                        <div class="progress progress-bar-default">
                                            <div id="progressbar_processa_medicao" style="width: 0%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="0" role="progressbar" class="progress-bar">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <label id="registro_atual" class="control-label">[0/0] Registros</label>
                                        <div class="progress progress-bar-default">
                                            <div id="progressbar_processa_registro" style="width: 0%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="0" role="progressbar" class="progress-bar">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br />

                                <div class="row">
                                    <div class="col-lg-12" style="text-align:center;">
                                        <div class="spiner-aguarde">
                                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                                <div class="sk-spinner sk-spinner-wave">
                                                    <div class="sk-rect1"></div>
                                                    <div class="sk-rect2"></div>
                                                    <div class="sk-rect3"></div>
                                                    <div class="sk-rect4"></div>
                                                    <div class="sk-rect5"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br />
                            </div>

                            <div id="div_aguarde_modelo" class="form-group col-lg-12" style="display:none; height:550px; padding-top:60px;">

                                <div class="row">
                                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                        <i class="fa fa-gears fa-5x"></i>
                                    </div>
                                </div>
                                <br />
                                <div class="row">
                                    <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                        <h2>Processando Modelo de Consumos de Utilidades Mensal</h2>
                                    </div>
                                </div>
                                <br />
                                <br />
                                <div class="row">
                                    <div class="col-lg-12" style="text-align:center;">
                                        <div class="spiner-aguarde">
                                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                                <div class="sk-spinner sk-spinner-wave">
                                                    <div class="sk-rect1"></div>
                                                    <div class="sk-rect2"></div>
                                                    <div class="sk-rect3"></div>
                                                    <div class="sk-rect4"></div>
                                                    <div class="sk-rect5"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br />
                            </div>
                        </div>

                        <br /><br />

                        <div class="div_resultado form-group col-lg-12" style="display:none;">
                        </div>

                        <br /><br />

                    </div>
                </div>
               
            }

        </div>
    </div>
</div>


@section Styles {
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/sweetAlert")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
                <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
                <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.es.min.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
                <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
                <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.pt-BR.min.js"></script>
    }

    <script type="text/javascript">

        var jqXHRData;

        $(document).ready(function () {

            $('#fileupload_Historico').fileupload({
                url: '/Upload/Upload_UtilidadesMensal_FileUploadXLS',
                dataType: 'json',
                add: function (e, data) {
                    jqXHRData = data;
                    jqXHRData.submit();
                },
                done: function (e, data) {

                    setTimeout(function () {

                        $('#progress_Historico .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        if (data.result.isUploaded) {

                            $("#nomeHistorico").val(data.files[0].name);

                            // processa Historico
                            ProcessaHistorico(data.files[0].name);

                        }
                        else {

                            swal({
                                html: true,
                                title: "Erro",
                                text: data.result.message,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });

                        }

                    }, 100);

                },
                fail: function (event, data) {
                    if (data.files[0].error) {

                        swal({
                            title: "Erro no envio",
                            text: data.files[0].error,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });
                    }
                },
                progressall: function (e, data) {
                    var progress = parseInt(data.loaded / data.total * 100, 10);
                    $('#progress_Historico .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                }
            }).prop('disabled', !$.support.fileInput)
                    .parent().addClass($.support.fileInput ? undefined : 'disabled');

        });


        function updateProgress(taskId, status_medicao, status_registro) {

            // barra de progresso das medicoes
            var elem = document.getElementById("progressbar_processa_medicao");
            elem.style.width = status_medicao + '%';
            elem.innerHTML = status_medicao * 1 + '%';

            // barra de progresso dos registros
            elem = document.getElementById("progressbar_processa_registro");
            elem.style.width = status_registro + '%';
            elem.innerHTML = status_registro * 1 + '%';
        }

        function ProcessaHistorico(nomeHistorico) {

            // aguarde
            $('#div_selecione').css("display", "none");
            $('#div_aguarde').css("display", "block");
            $('.div_resultado').css("display", "none");

            $.ajax(
            {
                type: 'GET',
                url: '/Upload/ProcessaUtilidadesMensal_IniciaProcesso',
                dataType: 'html',
                data: { 'nomeHistorico': nomeHistorico },
                cache: false,
                async: true,
                success: function (taskIdOriginal) {

                    var taskId = taskIdOriginal.replace(/['"]+/g, '');

                    // inicia barra progresso
                    updateProgress(taskId, "0", "0");

                    // atualiza barra de progresso
                    var intervalId = setInterval(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Upload/ProcessaUtilidadesMensal_Progress',
                            contentType: 'application/json; charset=utf-8',
                            dataType: 'json',
                            data: { 'id': taskId },
                            cache: false,
                            async: true,
                            success: function (progress) {

                                // medicao
                                document.getElementById("medicao_atual").innerHTML = progress.status_medicao;

                                // registro
                                document.getElementById("registro_atual").innerHTML = progress.status_registro;

                                // verifica se terminou
                                if (progress.progresso_medicao >= 100) {

                                    // fim barra de progresso
                                    updateProgress(taskId, "100", "100");
                                    clearInterval(intervalId);

                                    $('#div_selecione').css("display", "none");
                                    $('#div_aguarde').css("display", "none");
                                    $('.div_resultado').css("display", "block");

                                    // apresenta faturas
                                    $.ajax(
                                    {
                                        type: 'GET',
                                        url: '/Upload/_ProcessaUtilidadesMensal_Resultado',
                                        dataType: 'html',
                                        data: { 'id': taskId },
                                        cache: false,
                                        async: true,
                                        success: function (data) {

                                            swal({
                                                title: "Processado",
                                                type: "success",
                                                confirmButtonColor: "#18a689",
                                                confirmButtonText: "Fechar",
                                            }, function () {

                                            });

                                            $('.div_resultado').html(data);

                                        },
                                        error: function (xhr, status, error) {

                                            swal({
                                                html: true,
                                                title: "Erro",
                                                text: xhr.responseText,
                                                type: "warning",
                                                confirmButtonColor: "#f8ac59",
                                                confirmButtonText: "Fechar",
                                            }, function () {

                                            });

                                        }
                                    });

                                } else {

                                    // atualiza barra de progresso
                                    updateProgress(taskId, progress.progresso_medicao, progress.progresso_registro);
                                }

                            },
                            error: function (xhr, status, error) {

                                alert(xhr.responseText);
                            }
                        });

                    }, 1000);
                },
                error: function (xhr, status, error) {

                    alert(xhr.responseText);
                }
            });
        }


        function DownloadModelo() {
            event.stopPropagation();

            // aguarde
            $('#div_selecione').css("display", "none");
            $('#div_aguarde_modelo').css("display", "block");
            $('.div_resultado').css("display", "none");

            $.ajax(
            {
                type: 'POST',
                url: '/Upload/Upload_UtilidadesMensal_Modelo',
                contentType: "application/json;charset=utf-8",
                dataType: "json",
                cache: false,
                async: true,
                success: function (data) {

                    // inicia download
                    window.location = '/Upload/Upload_UtilidadesMensal_Modelo_Download?fileGuid=' + data.FileGuid
                                        + '&filename=' + data.FileName;

                    // fim
                    $('#div_selecione').css("display", "block");
                    $('#div_aguarde_modelo').css("display", "none");

                },
                error: function (xhr, status, error) {

                    // fim
                    $('#div_selecione').css("display", "block");
                    $('#div_aguarde_modelo').css("display", "none");

                    swal({
                        title: "Erro",
                        text: "Erro ao gerar XLS!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {
                    });

                    alert(xhr.responseText);
                }
            });
        }

    </script>
}


