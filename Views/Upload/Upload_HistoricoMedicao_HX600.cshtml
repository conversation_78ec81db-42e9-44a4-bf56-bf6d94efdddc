﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes
@using System.Globalization

@{
    ViewBag.Title = "HX600";
}

<head>
    <title>HX600</title>
    <style>

        .link_branco a:link {
            color: #ffffff !important;
        }
        .link_branco a:visited {
            color: #ffffff  !important;
        }
        .link_branco a:hover {
            color: #f0f0f0  !important;
        }
        .link_branco a:active {
            color: #ffffff  !important;
        }

    </style>
</head>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Upload_HistoricoMedicao_HX600", "Upload", FormMethod.Post, new { id = "form", role = "form" }))
            {

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>Enviar @SmartEnergy.Resources.MenuTexts.MenuLateralHistoricoMedicao HX600</h4>
                    </div>

                    <div class="panel-body">

                        @{
                            int IDCliente = ViewBag._IDCliente;
                            string NomeCliente = ViewBag.NomeCliente;

                            int IDMedicao = ViewBag._IDMedicao;
                            string NomeMedicao = ViewBag.NomeMedicao;

                            int IDTipoMedicao = ViewBag._IDTipoMedicao;
                        }


                        <div class="row">
                            <div class="form-group col-lg-6">
                                <h2>Atualizar o Histórico da medição selecionada com o MDB do HX600</h2><br />
                                <p>Formato do nome do MDB: <b>DmmmMMAA.mdb</b></p>
                                <p><b>mmm</b> = número da medição do HX600</p>
                                <p><b>MM</b> = mês do histórico</p>
                                <p><b>AA</b> = ano do histórico</p>
                                <p>Exemplo: <b>D0010820.mdb</b> = Histórico da medição 001 de Agosto/2020</p>
                            </div>
                            <div class="form-group col-lg-6">
                                <br />
                                <div class="row">
                                    <div class="form-group col-lg-4">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.IDCliente</label>
                                        @Html.TextBox("IDCliente", @IDCliente, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="form-group col-lg-8">
                                        <label class="control-label">&nbsp;Cliente</label>
                                        @Html.TextBox("NomeCliente", @NomeCliente, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                </div>
                                <br />
                                <div class="row">
                                    <div class="form-group col-lg-4">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.IDMedicao</label>
                                        @Html.TextBox("IDMedicao", @IDMedicao, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                    <div class="form-group col-lg-8">
                                        <label class="control-label">&nbsp;Medição</label>
                                        @Html.TextBox("NomeMedicao", @NomeMedicao, new { @class = "form-control", @disabled = "disabled" })
                                    </div>
                                </div>
                            </div>
                        </div>

                        <br />

                        @{
                            if (IDTipoMedicao == TIPO_MEDICAO.ENERGIA)
                            {
                                <div class="row">
                                    <div class="form-group col-lg-3">
                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.MenuTexts.MenuLateralHistoricoMedicao HX600 (ZIP ou MDB)</label><br />
                                        <input class="form-control" disabled="disabled" id="nomeHistorico" name="nomeHistorico" type="text" value="" />
                                    </div>
                                    <div class="form-group col-lg-3">
                                        <div id="progress_Historico" class="progress">
                                            <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                        </div>
                                        <span id="BotaoUpload" class="btn btn-primary fileinput-button" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">
                                            <span>Selecione o arquivo ZIP ou MDB</span>
                                            <input type="file" id="fileupload_Historico" name="fileupload_Historico">
                                        </span>
                                    </div>
                                    <div class="form-group col-lg-6">
                                        <h2><b>ZIP</b> colocar apenas os arquivos MDB de <b>mesma</b> medição do HX600</h2>
                                    </div>
                                </div>

                                <div class="row">
                                    <div id="div_selecione" class="form-group col-lg-12" style="display:block; height:250px; padding-top:40px;">
                                        <br />
                                        <div class="row">
                                            <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                                <i class="fa fa-upload fa-5x"></i>
                                            </div>
                                        </div>
                                        <br />
                                        <div class="row">
                                            <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                                <h2>Selecionar o arquivo com o @SmartEnergy.Resources.MenuTexts.MenuLateralHistoricoMedicao do HX600</h2>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="div_aguarde" class="form-group col-lg-12" style="display:none; height:250px; padding-top:40px;">
                                        <br />
                                        <div class="row">
                                            <div class="col-lg-6" style="text-align:center;">
                                                <div class="row">
                                                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                                        <i class="fa fa-gears fa-5x"></i>
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                                        <h2>Processando @SmartEnergy.Resources.MenuTexts.MenuLateralHistoricoMedicao HX600</h2>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <label id="arquivo_atual" class="control-label">[0/0] ---</label>
                                                        <div class="progress progress-bar-default">
                                                            <div id="progressbar_processa_arquivo" style="width: 0%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="0" role="progressbar" class="progress-bar">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <label id="registro_atual" class="control-label">[0/0] Registros</label>
                                                        <div class="progress progress-bar-default">
                                                            <div id="progressbar_processa_registro" style="width: 0%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="0" role="progressbar" class="progress-bar">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <br />

                                            <div class="row">
                                                <div class="col-lg-offset-6 col-lg-6" style="text-align:center;">
                                                    <div class="spiner-aguarde">
                                                        <div class="sk-spinner sk-spinner-wandering-cubes">
                                                            <div class="sk-spinner sk-spinner-wave">
                                                                <div class="sk-rect1"></div>
                                                                <div class="sk-rect2"></div>
                                                                <div class="sk-rect3"></div>
                                                                <div class="sk-rect4"></div>
                                                                <div class="sk-rect5"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                        <br />
                                    </div>
                                </div>

                                <br />

                                <div class="div_resultado form-group col-lg-12" style="display:none;">
                                </div>
                            }
                            else
                            {
                                <div class="row">
                                    <div class="form-group col-lg-12">
                                        <label class="control-label">&nbsp;Medição deve ser de Energia Elétrica</label><br />
                                        <label class="control-label">&nbsp;Selecionar uma outra Medição destino</label><br />
                                    </div>
                                </div>
                            }
                        }

                    </div>
                </div>
               
            }

        </div>
    </div>
</div>


@section Styles {
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/sweetAlert")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
                <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
                <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.es.min.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
                <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
                <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.pt-BR.min.js"></script>
    }

    <script type="text/javascript">

        var jqXHRData;

        $(document).ready(function () {

            $('#fileupload_Historico').fileupload({
                url: '/Upload/Upload_HistoricoMedicao_HX600_FileUploadMDB',
                dataType: 'json',
                add: function (e, data) {
                    jqXHRData = data;
                    jqXHRData.submit();
                },
                done: function (e, data) {

                    setTimeout(function () {

                        $('#progress_Historico .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        if (data.result.isUploaded) {

                            $("#nomeHistorico").val(data.files[0].name);

                            // processa Historico
                            ProcessaHistorico(data.files[0].name);

                        }
                        else {

                            swal({
                                html: true,
                                title: "Erro",
                                text: data.result.message,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });

                        }

                    }, 100);

                },
                fail: function (event, data) {
                    if (data.files[0].error) {

                        swal({
                            title: "Erro no envio",
                            text: data.files[0].error,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });
                    }
                },
                progressall: function (e, data) {
                    var progress = parseInt(data.loaded / data.total * 100, 10);
                    $('#progress_Historico .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                }
            }).prop('disabled', !$.support.fileInput)
                    .parent().addClass($.support.fileInput ? undefined : 'disabled');

        });


        function updateProgress(taskId, status_arquivo, status_registro) {

            // barra de progresso dos arquivos
            var elem = document.getElementById("progressbar_processa_arquivo");
            elem.style.width = status_arquivo + '%';
            elem.innerHTML = status_arquivo * 1 + '%';

            // barra de progresso dos registros
            elem = document.getElementById("progressbar_processa_registro");
            elem.style.width = status_registro + '%';
            elem.innerHTML = status_registro * 1 + '%';
        }

        function ProcessaHistorico(nomeHistorico) {

            // aguarde
            $('#div_selecione').css("display", "none");
            $('#div_aguarde').css("display", "block");
            $('.div_resultado').css("display", "none");

            $.ajax(
            {
                type: 'GET',
                url: '/Upload/ProcessaHistoricoMedicao_HX600_IniciaProcesso',
                dataType: 'html',
                data: { 'nomeHistorico': nomeHistorico },
                cache: false,
                async: true,
                success: function (taskIdOriginal) {

                    var taskId = taskIdOriginal.replace(/['"]+/g, '');

                    // inicia barra progresso
                    updateProgress(taskId, "0", "0");

                    // atualiza barra de progresso
                    var intervalId = setInterval(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Upload/ProcessaHistoricoMedicao_HX600_Progress',
                            contentType: 'application/json; charset=utf-8',
                            dataType: 'json',
                            data: { 'id': taskId },
                            cache: false,
                            async: true,
                            success: function (progress) {

                                // arquivo
                                document.getElementById("arquivo_atual").innerHTML = progress.status_arquivo;

                                // registro
                                document.getElementById("registro_atual").innerHTML = progress.status_registro;

                                // verifica se terminou
                                if (progress.progresso_arquivo >= 100) {

                                    // fim barra de progresso
                                    updateProgress(taskId, "100", "100");
                                    clearInterval(intervalId);

                                    $('#div_selecione').css("display", "none");
                                    $('#div_aguarde').css("display", "none");
                                    $('.div_resultado').css("display", "block");

                                    // apresenta faturas
                                    $.ajax(
                                    {
                                        type: 'GET',
                                        url: '/Upload/_ProcessaHistoricoMedicao_HX600_Resultado',
                                        dataType: 'html',
                                        data: { 'id': taskId },
                                        cache: false,
                                        async: true,
                                        success: function (data) {

                                            swal({
                                                title: "Processado",
                                                type: "success",
                                                confirmButtonColor: "#18a689",
                                                confirmButtonText: "Fechar",
                                            }, function () {

                                            });

                                            $('.div_resultado').html(data);

                                        },
                                        error: function (xhr, status, error) {

                                            swal({
                                                html: true,
                                                title: "Erro",
                                                text: xhr.responseText,
                                                type: "warning",
                                                confirmButtonColor: "#f8ac59",
                                                confirmButtonText: "Fechar",
                                            }, function () {

                                            });

                                        }
                                    });

                                } else {

                                    // atualiza barra de progresso
                                    updateProgress(taskId, progress.progresso_arquivo, progress.progresso_registro);
                                }

                            },
                            error: function (xhr, status, error) {

                                alert(xhr.responseText);
                            }
                        });

                    }, 1000);
                },
                error: function (xhr, status, error) {

                    alert(xhr.responseText);
                }
            });
        }

    </script>
}


