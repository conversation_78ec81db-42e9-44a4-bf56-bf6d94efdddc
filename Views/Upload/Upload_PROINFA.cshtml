﻿@using SmartEnergyLib.SQL
@using System.Globalization

@{
    ViewBag.Title = "PROINFA";
}

<head>
    <title>PROINFA</title>
</head>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("PROINFA", "Upload", FormMethod.Post, new { id = "form", role = "form" }))
            {

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>Enviar PROINFA</h4>
                    </div>

                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-3">
                                <label class="control-label">&nbsp;Planilha PROINFA</label><br />
                                <input class="form-control" disabled="disabled" id="nomePROINFA" name="nomePROINFA" type="text" value="" />
                            </div>
                            <div class="row">
                                <div class="form-group col-lg-3">
                                    <div id="progress_PROINFA" class="progress">
                                        <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                    </div>
                                    <span id="BotaoUpload" class="btn btn-primary fileinput-button" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">
                                        <span>Selecione a planilha PROINFA</span>
                                        <input type="file" id="fileupload_PROINFA" name="fileupload_PROINFA">
                                    </span>
                                </div>
                            </div>
                            <br /><br /><br />
                        </div>

                        <div id="div_selecione" class="form-group col-lg-12" style="display:block; height:250px; padding-top:60px;">
                            <div class="row">
                                <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                    <i class="fa fa-upload fa-5x"></i>
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                    <h2>Selecionar a planilha PROINFA</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            }

        </div>
    </div>
</div>


@section Styles {
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/sweetAlert")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
                <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
                <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.es.min.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
                <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
                <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.pt-BR.min.js"></script>
    }

    <script type="text/javascript">

        var jqXHRData;

        $(document).ready(function () {

            $('#fileupload_PROINFA').fileupload({
                url: '/Configuracao/UploadFile_PROINFA',
                dataType: 'json',
                add: function (e, data) {
                    jqXHRData = data;
                    jqXHRData.submit();
                },
                done: function (e, data) {
                    if (data.result.isUploaded) {

                        setTimeout(function () {

                            $('#progress_PROINFA .progress-bar').css(
                                                    'width',
                                                    0 + '%'
                                                );

                            swal({
                                title: "Enviado com sucesso",
                                text: data.result.message,
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                $('#progress_PROINFA .progress-bar').css(
                                'width',
                                0 + '%'
                                );

                            });

                        }, 1000);
                    }
                    else {
                        setTimeout(function () {

                            swal({
                                title: "Erro no envio",
                                text: data.result.message,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                                $('#progress_PROINFA .progress-bar').css(
                                'width',
                                0 + '%'
                                );
                            });

                        }, 1000);
                    }

                },
                fail: function (event, data) {
                    if (data.files[0].error) {

                        swal({
                            title: "Erro no envio",
                            text: data.files[0].error,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });
                    }
                },
                progressall: function (e, data) {
                    var progress = parseInt(data.loaded / data.total * 100, 10);
                    $('#progress_PROINFA .progress-bar').css(
                        'width',
                        progress + '%'
                    );
                }
            }).prop('disabled', !$.support.fileInput)
                    .parent().addClass($.support.fileInput ? undefined : 'disabled');


        });

    </script>
}


