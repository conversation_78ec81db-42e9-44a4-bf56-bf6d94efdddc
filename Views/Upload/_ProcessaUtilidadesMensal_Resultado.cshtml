﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    List<PROCESSA_UTIL_MENSAL_RESULTADO> processa_resultado = ViewBag.processaUtilidadesMensal_resultado;
}

<div class="row">
    <div class="col-lg-12">

        <div class="panel panel-info">

            <div class="panel-body">

                <table id="dataTables-historico" class="table table-striped table-bordered table-hover dataTables-historico">
                    <thead>
                        <tr>
                            <th>Cliente</th>
                            <th>ID [Medição]</th>
                            <th>Medição</th>
                            <th>Status</th>
                            <th>Início</th>
                            <th>Fim</th>
                            <th>Número de Registros</th>
                        </tr>
                    </thead>
                    <tbody>

                        @{
                            if (processa_resultado != null)
                            {
                                foreach (PROCESSA_UTIL_MENSAL_RESULTADO result in processa_resultado)
                                {
                                    <tr>
                                        <td style="font-weight:900;">@result.NomeCliente</td>
                                        <td style="font-weight:900;">@result.IDMedicao</td>
                                        <td style="font-weight:900;">@result.NomeMedicao</td>

                                        @{
                                            string nome_status = "";
                                            string linha_status = string.Format("(Linha {0})", result.linha);

                                            if (result.status == PROCESSA_UTIL_MENSAL_STATUS.PROCESSO_OK)
                                            {
                                                nome_status = "Processado OK";
                                                
                                                <td>@nome_status</td>
                                                <td>@string.Format("{0:dd/MM/yyyy HH:mm}", result.inicio)</td>
                                                <td>@string.Format("{0:dd/MM/yyyy HH:mm}", result.fim)</td>
                                                <td>@result.NumRegistros</td>
                                            }
                                            else
                                            {
                                                switch(result.status)
                                                {
                                                    default:
                                                        nome_status = result.status.ToString();
                                                        break;

                                                    case PROCESSA_UTIL_MENSAL_STATUS.MED_INEX:
                                                        nome_status = "Medição Inexistente";
                                                        break;

                                                    case PROCESSA_UTIL_MENSAL_STATUS.MED_NO_UTIL:
                                                        nome_status = "Medição não é Utilidades";
                                                        break;
                                                        
                                                    case PROCESSA_UTIL_MENSAL_STATUS.GATE_INEX:
                                                        nome_status = "Gateway Inexistente";
                                                        break;                                                        
                                                        
                                                    case PROCESSA_UTIL_MENSAL_STATUS.GATE_NO_VIRT:
                                                        nome_status = "Gateway não é Virtual";
                                                        break;                                                                                                                
                                                                                                                
                                                    case PROCESSA_UTIL_MENSAL_STATUS.DATA_INVALIDA:
                                                        nome_status = "Data inválida";
                                                        break;

                                                    case PROCESSA_UTIL_MENSAL_STATUS.MAIOR_HOJE:
                                                        nome_status = "Datas não podem estar no futuro";
                                                        break;
                                                        
                                                    case PROCESSA_UTIL_MENSAL_STATUS.MAIOR_60DIAS:
                                                        nome_status = "Diferença entre data Final e data Inicial não pode ser maior que 60 dias";
                                                        break;
                                                                                                                
                                                    case PROCESSA_UTIL_MENSAL_STATUS.ERRO_SALVAR_REG:
                                                        nome_status = "Erro ao salvar registro";
                                                        break;
                                                }
                                                
                                                <td>@nome_status<br />@linha_status</td>
                                                <td>---</td>
                                                <td>---</td>
                                                <td>---</td>
                                            }
                                        }

                                    </tr>
                                }
                            }
                        }

                    </tbody>
                </table>

            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "---") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-historico').DataTable({
            "bAutoWidth": false,
            "iDisplayLength": 10,
            dom: 'tp',
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "numero" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "numero" },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "aoColumns": [
            { sWidth: "14%" },
            { sWidth: "14%" },
            { sWidth: "14%" },
            { sWidth: "14%" },
            { sWidth: "14%" },
            { sWidth: "14%" },
            { sWidth: "14%" },
            ],
            'order': [[0, 'asc'], [2, 'asc']],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

    });

</script>
