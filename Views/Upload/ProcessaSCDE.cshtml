﻿@model SmartEnergyLib.SQL.ProcessaSCDEDominio

@using SmartEnergyLib.SQL
@using System.Globalization

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralProcessaSCDE;
}

<head>
    <title>SCDE</title>
</head>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("ProcessaSCDE", "Upload", FormMethod.Post, new { id = "form", role = "form" }))
            {

                <div class="panel panel-title processa">
                    <div class="panel-heading">
                        <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralProcessaSCDE</h4>
                    </div>

                    <div class="panel-body">

                        <div class="row">
                            <div class="form-group col-lg-6">
                                <label class="control-label">&nbsp;Arquivo SCDE</label><br />
                                @Html.TextBoxFor(model => model.nomeSCDE, new { @class = "form-control", @disabled = "disabled" })
                            </div>
                            <div class="form-group col-lg-6" id="BotaoUploadDIV">
                                <div id="progress_up" class="progress">
                                    <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                </div>
                                <span id="BotaoUpload" class="btn btn-primary fileinput-button" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">
                                    <span>Selecione o arquivo SCDE (CSV ou ZIP)</span>
                                    @Html.TextBoxFor(model => model.nomeSCDE, new { id = "fileupload", name = "fileupload", type = "file" })
                                </span>
                            </div>
                        </div>

                        <div class="row">
                            <div id="relat_aguarde" class="form-group col-lg-12" style="display:none; height:550px; padding-top:60px;">

                                <div class="row">
                                    <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                        <i class="fa fa-gears fa-5x"></i>
                                    </div>
                                </div>
                                <br />
                                <div class="row">
                                    <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                        <h2>Processando arquivos SCDE</h2>
                                    </div>
                                </div>
                                <br />
                                <br />

                                <div class="row">
                                    <div class="col-lg-12">
                                        <label id="arquivo_atual" class="control-label">[0/0] ---</label>
                                        <div class="progress progress-bar-default">
                                            <div id="progressbar_processa_arquivo" style="width: 0%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="0" role="progressbar" class="progress-bar">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <label id="registro_atual" class="control-label">[0/0] Registros</label>
                                        <div class="progress progress-bar-default">
                                            <div id="progressbar_processa_registro" style="width: 0%" aria-valuemax="100" aria-valuemin="0" aria-valuenow="0" role="progressbar" class="progress-bar">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br />

                                <div class="row">
                                    <div class="col-lg-12" style="text-align:center;">
                                        <div class="spiner-aguarde">
                                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                                <div class="sk-spinner sk-spinner-wave">
                                                    <div class="sk-rect1"></div>
                                                    <div class="sk-rect2"></div>
                                                    <div class="sk-rect3"></div>
                                                    <div class="sk-rect4"></div>
                                                    <div class="sk-rect5"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br />
                            </div>
                        </div>

                        <br /><br />

                        <div class="div_resultado form-group col-lg-12" style="display:none;">
                        </div>

                        <div class="div_status form-group col-lg-12" style="display:block;">

                            <div class="row">
                                <div class="col-lg-12">

                                    <div class="panel panel-info">

                                        <div class="panel-heading">
                                            <h4>Últimas Atualizações SCDE</h4>
                                        </div>

                                        <div class="panel-body">

                                            <table class="table table-striped table-bordered table-hover dataTables-scde-status">
                                                <thead>
                                                    <tr>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.Logo</th>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.Clientes</th>
                                                        <th>@SmartEnergy.Resources.ConfiguracaoTexts.PontoMedicao</th>
                                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                                    </tr>
                                                </thead>
                                                <tbody>

                                                    @{
                                                        List<SupervSCDEDominio> processaSCDE_status = ViewBag.listaSupervSCDE;
                                                        
                                                        if (processaSCDE_status != null)
                                                        {
                                                            foreach (SupervSCDEDominio status in processaSCDE_status)
                                                            {
                                                                string logo = "http://www.smartenergy.com.br/";

                                                                if (status.LogoCliente.IsEmpty())
                                                                {
                                                                    logo += "/Logos/LogoSmartEnergy.png";
                                                                }
                                                                else
                                                                {
                                                                    logo += "/Logos/" + status.LogoCliente;
                                                                }

                                                                // data e hora
                                                                string DataHora = String.Format("{0:G}", status.UltimaSuperv);
                                                                string DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", status.UltimaSuperv);

                                                                <tr>
                                                                    <td bgcolor="#293846" align="center"><img src=@logo style="max-height:45px; height: auto;" /></td>
                                                                    <td><b>@status.FantasiaCliente</b><br />@status.NomeCliente</td>
                                                                    <td>@status.PontoMedicao</td>
                                                                    <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                                                </tr>

                                                            }
                                                        }
                                                    }

                                                </tbody>
                                            </table>

                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <br /><br />

                    </div>
                </div>

            }

        </div>
    </div>
</div>


@section Styles {
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/sweetAlert")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
                <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
                <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.es.min.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
                <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
                <script type="text/javascript" language="javascript" src="~/Scripts/locales/bootstrap-datepicker.pt-BR.min.js"></script>
    }

    <script type="text/javascript">

        var jqXHRData;

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {

                    var x = a;

                    if (a == "---") {
                        x = "-1";
                    }
                    else {
                        x = a.replace(".", "");
                        x = x.replace(",", ".");
                    }

                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-scde-status').DataTable({
                "bAutoWidth": false,
                "iDisplayLength": 10,
                dom: 'ftp',
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "portugues", "bSortable": false, "bSearchable": false },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "portugues" },
                ],
                "oSearch": {
                    "bSmart": false,
                    "bRegex": true,
                    "sSearch": ""
                },
                "aoColumns": [
                { sWidth: "10%" },
                { sWidth: "30%" },
                { sWidth: "30%" },
                { sWidth: "30%" },
                ],
                'order': [[3, 'asc'],[1, 'asc']],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            $('#fileupload').fileupload({
                url: '/Upload/UploadFileSCDE',
                dataType: 'json',
                add: function (e, data) {
                    jqXHRData = data;
                    jqXHRData.submit();
                },
                done: function (e, data) {

                    setTimeout(function () {

                        $('#progress_up .progress-bar_up').css(
                                                'width',
                                                0 + '%'
                                            );

                        if (data.result.isUploaded) {

                            $("#nomeSCDE").val(data.files[0].name);

                            // processa SCDE
                            ProcessaSCDE(data.files[0].name);

                        }
                        else {

                            swal({
                                html: true,
                                title: "Erro",
                                text: data.result.message,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });

                        }

                    }, 100);

                },
                fail: function (event, data) {
                    if (data.files[0].error) {
                        alert(data.files[0].error);
                    }
                },
                progressall: function (e, data) {
                    var progress = parseInt(data.loaded / data.total * 100, 10);
                    $('#progress_up .progress-bar_up').css(
                        'width',
                        progress + '%'
                    );
                }
            }).prop('disabled', !$.support.fileInput)
                    .parent().addClass($.support.fileInput ? undefined : 'disabled');

        });



        function updateProgress(taskId, status_arquivo, status_registro) {

            // barra de progresso dos arquivos
            var elem = document.getElementById("progressbar_processa_arquivo");
            elem.style.width = status_arquivo + '%';
            elem.innerHTML = status_arquivo * 1 + '%';

            // barra de progresso dos registros
            elem = document.getElementById("progressbar_processa_registro");
            elem.style.width = status_registro + '%';
            elem.innerHTML = status_registro * 1 + '%';
        }

        function ProcessaSCDE(nomeSCDE) {

            // aguarde
            $('#relat_aguarde').css("display", "block");
            $('.div_status').css("display", "none");
            $('.div_resultado').css("display", "none");

            $.ajax(
            {
                type: 'GET',
                url: '/Upload/ProcessaSCDE_IniciaProcesso',
                dataType: 'html',
                data: { 'nomeSCDE': nomeSCDE },
                cache: false,
                async: true,
                success: function (taskIdOriginal) {

                    var taskId = taskIdOriginal.replace(/['"]+/g, '');

                    // inicia barra progresso
                    updateProgress(taskId, "0", "0");

                    // atualiza barra de progresso
                    var intervalId = setInterval(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Upload/ProcessaSCDE_Progress',
                            contentType: 'application/json; charset=utf-8',
                            dataType: 'json',
                            data: { 'id': taskId },
                            cache: false,
                            async: true,
                            success: function (progress) {

                                // arquivo
                                document.getElementById("arquivo_atual").innerHTML = progress.status_arquivo;

                                // registro
                                document.getElementById("registro_atual").innerHTML = progress.status_registro;

                                // verifica se terminou
                                if (progress.progresso_arquivo >= 100) {

                                    // fim barra de progresso
                                    updateProgress(taskId, "100", "100");
                                    clearInterval(intervalId);

                                    $('#relat_aguarde').css("display", "none");
                                    $('.div_status').css("display", "none");
                                    $('.div_resultado').css("display", "block");

                                    // apresenta resultado
                                    $.ajax(
                                    {
                                        type: 'GET',
                                        url: '/Upload/_ProcessaSCDE_Resultado',
                                        dataType: 'html',
                                        data: { 'id': taskId },
                                        cache: false,
                                        async: true,
                                        success: function (data) {

                                            swal({
                                                title: "Processado com sucesso",
                                                type: "success",
                                                confirmButtonColor: "#18a689",
                                                confirmButtonText: "Fechar",
                                            }, function () {

                                            });

                                            $('.div_resultado').html(data);

                                        },
                                        error: function (xhr, status, error) {

                                            swal({
                                                html: true,
                                                title: "Erro",
                                                text: xhr.responseText,
                                                type: "warning",
                                                confirmButtonColor: "#f8ac59",
                                                confirmButtonText: "Fechar",
                                            }, function () {

                                            });

                                        }
                                    });

                                } else {

                                    // atualiza barra de progresso
                                    updateProgress(taskId, progress.progresso_arquivo, progress.progresso_registro);
                                }

                            },
                            error: function (xhr, status, error) {

                                alert(xhr.responseText);
                            }
                        });

                    }, 1000);
                },
                error: function (xhr, status, error) {

                    alert(xhr.responseText);
                }
            });
        }


    </script>
}


