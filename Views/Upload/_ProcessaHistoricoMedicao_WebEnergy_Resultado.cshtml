﻿@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    List<PROCESSA_HIST_MED_RESULTADO> processa_resultado = ViewBag.processaHistoricoMedicao_resultado;
}

<div class="row">
    <div class="col-lg-12">

        <div class="panel panel-info">

            <div class="panel-body">

                <table id="dataTables-historico" class="table table-striped table-bordered table-hover dataTables-historico">
                    <thead>
                        <tr>
                            <th>Arquivo</th>
                            <th>Cliente</th>
                            <th>ID [Medição]</th>
                            <th>Medição</th>
                            <th>Status</th>
                            <th>Início</th>
                            <th>Fim</th>
                            <th>Número de Registros</th>
                        </tr>
                    </thead>
                    <tbody>

                        @{
                            if (processa_resultado != null)
                            {
                                foreach (PROCESSA_HIST_MED_RESULTADO result in processa_resultado)
                                {
                                    <tr>
                                        <td style="font-weight:900;">@result.NomeArquivo</td>

                                        @{
                                            string nome_status = "";
                                            string linha_status = string.Format("(Linha {0})", result.linha);
                                            string coluna_status = "";

                                            if (result.status == PROCESSA_HIST_MED_STATUS.PROCESSO_OK)
                                            {
                                                nome_status = "Processado OK";

                                                <td style="font-weight:900;">@result.NomeCliente</td>
                                                <td style="font-weight:900;">@result.IDMedicao</td>
                                                <td style="font-weight:900;">@result.NomeMedicao</td>
                                                <td><span style="color:green;"><strong>@nome_status</strong></span><br />&nbsp;</td>
                                                <td>@string.Format("{0:dd/MM/yyyy HH:mm}", result.inicio)</td>
                                                <td>@string.Format("{0:dd/MM/yyyy HH:mm}", result.fim)</td>
                                                <td>@result.NumRegistros</td>
                                            }
                                            else
                                            {
                                                switch (result.status)
                                                {
                                                    default:
                                                        nome_status = result.status.ToString();
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.MED_INEX:
                                                        nome_status = "Medição Inexistente";
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.MED_MUDOU:
                                                        nome_status = "Mais de uma medição na planilha";
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.CLI_DIF:
                                                        nome_status = "Cliente diferente do cliente da Medição";
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.ERRO_SALVAR_REG:
                                                        nome_status = "Erro ao salvar registro";
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.ERRO_ARQUIVO:
                                                        nome_status = "Erro no formato da Tabela";
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.ERRO_NOME_ARQUIVO:
                                                        nome_status = "Erro no formato do Nome do Arquivo";
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.ERRO_DATA_HORA:
                                                        nome_status = "Erro na Data e Hora";
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.SEM_REG:
                                                        nome_status = "Sem registros";
                                                        break;

                                                    case PROCESSA_HIST_MED_STATUS.ERRO_LEITURA:
                                                        nome_status = "Erro leitura da planilha";

                                                        switch (result.coluna)
                                                        {
                                                            case 1:
                                                                coluna_status = "Data Hora";
                                                                break;

                                                            case 2:
                                                                coluna_status = "Demanda Ativa";
                                                                break;

                                                            case 3:
                                                                coluna_status = "Demanda Reativa";
                                                                break;

                                                            case 6:
                                                                coluna_status = "Período";
                                                                break;
                                                        }

                                                        break;

                                                }

                                                if (result.status == PROCESSA_HIST_MED_STATUS.ERRO_ARQUIVO)
                                                {
                                                    <td style="font-weight:900;">---</td>
                                                    <td style="font-weight:900;">---</td>
                                                    <td style="font-weight:900;">---</td>
                                                    <td><span style="color:red;"><strong>@nome_status</strong></span><br />&nbsp;</td>
                                                }
                                                else
                                                {
                                                    <td style="font-weight:900;">@result.NomeCliente</td>
                                                    <td style="font-weight:900;">@result.IDMedicao</td>
                                                    <td style="font-weight:900;">@result.NomeMedicao</td>

                                                    if (coluna_status == "")
                                                    {
                                                        <td><span style="color:red;"><strong>@nome_status</strong></span><br />@linha_status<br />@result.erro</td>
                                                    }
                                                    else
                                                    {
                                                        <td><span style="color:red;"><strong>@nome_status</strong></span><br />@coluna_status<br />@linha_status<br />@result.erro</td>
                                                    }
                                                }

                                                <td>---</td>
                                                <td>---</td>
                                                <td>---</td>
                                            }
                                        }

                                    </tr>
                                }
                            }
                        }

                    </tbody>
                </table>

            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

    $(document).ready(function () {

                                                    jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                                                        "portugues-pre": function (data) {
                                                            var a = 'a';
                                                            var e = 'e';
                                                            var i = 'i';
                                                            var o = 'o';
                                                            var u = 'u';
                                                            var c = 'c';
                                                            var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                                                            };
                for (var val in special_letters)
                                                            data = data.split(val).join(special_letters[val]).toLowerCase();
                                                        return data;
                                                    },
            "portugues-asc": function (a, b) {
                                                        return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                                                    },
            "portugues-desc": function (a, b) {
                                                        return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                                                    }
                                                });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                                                    var x = a;

                                                    if (a == "---") {
                                                        x = "-1";
                                                    }
                                                    else {
                                                        x = a.replace(".", "");
                                                        x = x.replace(",", ".");
                                                    }

                                                    return parseFloat(x);
                                                },

            "numero-asc": function (a, b) {
                                                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                                                },

            "numero-desc": function (a, b) {
                                                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                                                }
                                            });

        $('.dataTables-historico').DataTable({
            "bAutoWidth": false,
            "iDisplayLength": 8,
            dom: 'tp',
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "numero" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "portugues" },
                        { "aTargets": [7], "sType": "numero" },
            ],
            "oSearch": {
                "bSmart": false,
                "bRegex": true,
                "sSearch": ""
            },
            "aoColumns": [
            { sWidth: "14%" },
            { sWidth: "14%" },
            { sWidth: "12%" },
            { sWidth: "14%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "12%" },
            { sWidth: "10%" },
            ],
            'order': [[1, 'asc'], [3, 'asc']],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });
    });

</script>
