﻿@using System.Globalization
@using SmartEnergyLib.SQL

@{  
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSuporteAnaliseArquivosProblemas;
}

<style>

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        margin: 0;
        position: absolute;
        top: 50%;
        left: 50%;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>



<div class="row wrapper border-bottom white-bg page-heading">
    <div class="row" style="margin:15px 5px 0px 5px;">
        <div class="col-lg-10">

            <div class="row">
                <div class="col-lg-1" style="text-align:center;">
                    <i class="fa fa-warning fa-5x"></i>
                </div>
                <div class="col-lg-11">
                    <h2>Esta operação busca e apresenta os arquivos recebidos da Gateway que apresentaram problemas.</h2>
                </div>
            </div>
            <br />
            <br />

            @using (Html.BeginForm("BuscaGatewayProblemas_Executar", "Manutencao", FormMethod.Post, new { id = "form", role = "form" }))
            {
                <div class="superv-content">
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="form-group">
                                <label class="control-label">&nbsp;IDGateway</label>
                                <div class="input-group">
                                    <input type='number' min="0" maxlength="6" class="form-control" id="id_gateway" value=@ViewBag.IDGateway_Search>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="form-group">
                                <span>&nbsp;IDGateway em zero apresenta todos os arquivos com problemas</span>
                            </div>
                        </div>
                    </div>
                </div>
            }
                            
        </div>
                
        <div class="col-lg-2">
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox-tools">
                        <button class="btn btn-default" onclick="window.history.go(-1)">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                        <button type="button" class="btn btn-primary" id="BotaoExecutar" onclick="Executar();">@SmartEnergy.Resources.ComumTexts.BotaoExecutar</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="overlay_aguarde" style="display: none">
            <div class="spinner-aguarde">
                <div class="sk-spinner sk-spinner-wandering-cubes">
                    <div class="sk-spinner sk-spinner-wave">
                        <div class="sk-rect1 text-ligh"></div>
                        <div class="sk-rect2"></div>
                        <div class="sk-rect3"></div>
                        <div class="sk-rect4"></div>
                        <div class="sk-rect5"></div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12" id="resultado">
            @Html.Partial("_BuscaGatewayProblemas_Resultado")
        </div>
    </div>
</div>



@section Styles {
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/Supervisao.css")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/validate")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    $(document).ready(function () {

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        $("#form").validate({
            rules: {
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        setTimeout(function () {

            Executar();

        }, 100);

    });

    function Executar() {

        var $form = $('#form');

        // verifica se entradas sao validas
        if (!$form.valid()) return false;

        // aguarde
        $('.overlay_aguarde').toggle();

        var IDGateway = parseInt(document.getElementById("id_gateway").value);
    
        $.ajax(
        {
            type: 'GET',
            url: '/Suporte/_BuscaGatewayProblemas_Resultado',
            dataType: 'html',
            data: { 'IDGateway': IDGateway },
            cache: false,
            async: true,
            success: function (data) {

                // fim
                $('.overlay_aguarde').toggle();

                // resultado
                $('#resultado').html(data);

            },
            error: function (xhr, status, error) {

                // fim
                $('.overlay_aguarde').toggle();

                setTimeout(function () {

                    swal({
                        title: "Erro",
                        text: "Erro ao executar a operação!",
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    });

                }, 100);

            }
        });
    }

</script>

}


