﻿@model IEnumerable<SmartEnergyLib.SQL.SupervGatewaysDominio>

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoGatewaysAtraso;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #333333 !important;
    }

    .icones-danger {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #ED5565 !important;
    }

    .icones-warning {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #f8ac59 !important;
    }

    .icones-primary {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #1ab394 !important;
    }

    .tipo1-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo2-bg {
        background-color: #C0C0C0 !important;
        color: #000000 !important;
    }

    .tipo3-bg {
        background-color: #A9A9A9 !important;
        color: #ffffff !important;
    }

    .tipo4-bg {
        background-color: #696969 !important;
        color: #ffffff !important;
    }

    .tipo5-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo6-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .dataTables-gateways tr.even:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    .dataTables-gateways tr.odd:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    .tooltip-inner {
        position: relative;
        display: inline-block;
        max-width: 350px;
        /* If max-width does not work, try using width instead */
        white-space: pre-wrap;
        text-align: left;
    }
</style>



<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoGatewaysAtraso</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-6">

                            @{  
                                var GatewaysAtraso = ViewBag.GatewaysAtraso;
                                var GatewaysAtraso_Classe = "navy-bg";

                                if (GatewaysAtraso >= 20)
                                {
                                    GatewaysAtraso_Classe = "red-bg";
                                }
                            }

                            <div class="widget style1 @GatewaysAtraso_Classe">
                                <div class="row">

                                    @{
                                        if (GatewaysAtraso >= 20)
                                        {
                                            <blink>
                                                <div class="col-xs-4">
                                                    <i class="fa fa-clock-o fa-5x"></i>
                                                </div>
                                                <div class="col-xs-8 text-right">
                                                    <span> Gateways em Atraso </span>
                                                    <h2 class="font-bold">@GatewaysAtraso</h2>
                                                </div>
                                            </blink>
                                        }
                                        else
                                        {   
                                            <div class="col-xs-4">
                                                <i class="fa fa-clock-o fa-5x"></i>
                                            </div>
                                            <div class="col-xs-8 text-right">
                                                <span>Gateways em Atraso </span>
                                                <h2 class="font-bold">@GatewaysAtraso</h2>
                                            </div>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo1-bg m-r-sm">@ViewBag.GatewaysAtraso_3dias</button>
                                            Atraso menor que 3 dias
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo2-bg m-r-sm">@ViewBag.GatewaysAtraso_3_7dias</button>
                                            Atraso de 3 a 7 dias
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo3-bg m-r-sm">@ViewBag.GatewaysAtraso_7_23dias</button>
                                            Atraso de 7 a 23 dias
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo4-bg m-r-sm">@ViewBag.GatewaysAtraso_23dias</button>
                                            Atraso acima de 23 dias
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo5-bg m-r-sm">@ViewBag.GatewaysRelogioErrado</button>
                                            Relógio da Gateway com diferença
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo6-bg m-r-sm">@ViewBag.GatewaysFaltaArquivo</button>
                                            Faltam arquivos
                                        </td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <select class="select2_tipo form-control" id="Tipos" onchange="SelecionouTipo()">
                                <option value='0'>Todas as Gateways em atraso</option>
                                <option value='1'>Atraso menor que 3 dias</option>
                                <option value='2'>Atraso de 3 a 7 dias</option>
                                <option value='3'>Atraso de 7 a 23 dias</option>
                                <option value='4'>Atraso maior que 23 dias</option>
                                <option value='5'>Gateways com diferença entre o relógio da gateway e do servidor maior que 2 horas</option>
                                <option value='6'>Gateways sem receber todos os arquivos</option>
                            </select>
                            <br />
                            <table id="example" class="table table-bordered table-hover dataTables-gateways">
                                <thead>
                                    <tr>
                                        <th>Tipo Atraso</th>
                                        <th>ID</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.ClientesNomeFantasia</th>
                                        <th>ID</th>
                                        <th>Gateway</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.Equipamento</th>
                                        <th><i class="fa fa-signal icones"></i> @SmartEnergy.Resources.SupervisaoTexts.Sinal</th>
                                        <th><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@SmartEnergy.Resources.SupervisaoTexts.Total @SmartEnergy.Resources.SupervisaoTexts.Falhas - @SmartEnergy.Resources.SupervisaoTexts.Ultimos 3 @SmartEnergy.Resources.SupervisaoTexts.Meses">@SmartEnergy.Resources.SupervisaoTexts.Falhas</span></th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{                                        
                                        foreach (SupervGatewaysDominio gateway in Model)
                                        {                                               
                                                                                        
                                            // listo historico de falhas
                                            AnaliseGatewaysHistMetodos analiseGatewaysMetodos = new AnaliseGatewaysHistMetodos();
                                            List<AnaliseGatewaysHistDominio> analiseGatewaysHist = analiseGatewaysMetodos.ListarPorIDGateway(gateway.IDGateway);

                                            // total de falhas dos ultimos 3 meses
                                            TimeSpan tresMeses = new TimeSpan(90, 0, 0, 0);
                                            int falhas_Total = analiseGatewaysHist.Count(x => (DateTime.Now - x.DataFim <= tresMeses));

                                            string Classe = "tipo" + gateway.TipoAtraso + "-bg";

                                            // copia data e hora
                                            string DataHora = String.Format("{0:G}", gateway.DataHora);
                                            string DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", gateway.DataHora);

                                            // copia data e hora equipamento
                                            string DataEq = String.Format("{0:G}", gateway.DataEq);
                                            string DataEq_Sort = String.Format("{0:yyyyMMddHHmmss}", gateway.DataEq);

                                            // sinal
                                            int sinal = gateway.Sinal;
                                            string SinalTexto = "Sinal Fraco";
                                            string SinalCor = "red";
                                            string sinalStr = "-";

                                            if (sinal >= 38 && sinal < 50)
                                            {
                                                SinalTexto = "Sinal Médio Fraco";
                                                SinalCor = "orange";
                                            }

                                            if (sinal >= 50 && sinal < 68)
                                            {
                                                SinalTexto = "Sinal Médio";
                                                SinalCor = "orange";
                                            }

                                            if (sinal >= 68 && sinal <= 100)
                                            {
                                                SinalTexto = "Sinal Forte";
                                                SinalCor = "green";
                                            }

                                            if (sinal >= 100)
                                            {
                                                sinal = 999;
                                            }

                                            if (sinal > 0)
                                            {
                                                sinalStr = string.Format("{0}%", gateway.Sinal);
                                            }

                                            
                                            <tr class="@Classe" style="cursor:pointer;" onclick="location.href='@("/Supervisao/Gateway?IDGateway=" + @gateway.IDGateway.ToString())'">
                                                <td>@gateway.TipoAtraso</td>
                                                <td>@gateway.IDCliente</td>
                                                <td>@gateway.Fantasia.ToUpper()</td>
                                                <td>@gateway.IDGateway</td>
                                                <td>@gateway.Nome</td>
                                                <td><span style="display:none;">@DataHora_Sort    </span>@DataHora</td>
                                                <td><span style="display:none;">@DataEq_Sort    </span>@DataEq</td>
                                                <td>
                                                    <span style="display:none;">@string.Format("{000}", sinal)</span>
                                                    <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@SinalTexto"><b>@sinalStr</b></span>
                                                </td>
                                                <td>@falhas_Total</td>
                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")

    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-gateways').DataTable({
                "iDisplayLength": 10,
                dom: 'Bftp',
                buttons: [
                    {
                        extend: 'excelHtml5',
                        filename: 'GatewaysAtraso',
                        title: 'Gateways com Atraso no Envio de Arquivos',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                    {
                        extend: 'pdfHtml5',
                        filename: 'GatewaysAtraso',
                        title: 'Gateways com Atraso no Envio de Arquivos',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                ],

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "bVisible": false },
                            { "aTargets": [1], "sType": "numero" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "numero" },
                            { "aTargets": [4], "sType": "portugues" },
                            { "aTargets": [5], "sType": "portugues" },
                            { "aTargets": [6], "sType": "portugues" },
                            { "aTargets": [7], "sType": "portugues" },
                            { "aTargets": [8], "sType": "numero" },
                ],
                "aoColumns": [
                { sWidth: "0%" },
                { sWidth: "5%" },
                { sWidth: "20%" },
                { sWidth: "5%" },
                { sWidth: "20%" },
                { sWidth: "12%" },
                { sWidth: "12%" },
                { sWidth: "15%" },
                { sWidth: "9%" },
                ],
                'order': [5, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            // lista somente gateways em atraso
            regExSearch = "^(?=.*?(1|2|3|4)).*?";
            $('.dataTables-gateways').DataTable().column(0).search(regExSearch, true, false).draw();

            $(".bottom").tooltip({
                placement: "bottom"
            });

            piscando();
        });

        function SelecionouTipo() {

            // pega tipo selecionado
            var tipo = document.getElementById("Tipos").selectedIndex;

            // verifica se todos
            if (tipo == 0) {
                // lista somente gateways em atraso
                regExSearch = "^(?=.*?(1|2|3|4)).*?";
                $('.dataTables-gateways').DataTable().column(0).search(regExSearch, true, false).draw();
            }
            else {
                // lista tipo selecionado
                $('.dataTables-gateways').DataTable().column(0).search(tipo).draw();
            }
        }

        function piscando() {

            var tempo = 500; //1000 = 1s

            blinks = document.getElementsByTagName("blink");
            for (var i = 0; i < blinks.length; i++) {
                if (blinks[i].getAttribute("style") == "VISIBILITY: hidden") {
                    blinks[i].setAttribute("style", "VISIBILITY: visible");
                } else {
                    blinks[i].setAttribute("style", "VISIBILITY: hidden");
                }
            }
            setTimeout('piscando()', tempo);
        }


    </script>
}

