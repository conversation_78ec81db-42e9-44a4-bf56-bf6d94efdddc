﻿@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

<style>

    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }
    .link_preto a:visited {
        color: #7E6A6C  !important;
    }
    .link_preto a:hover {
        color: #a0a0a0  !important;
    }
    .link_preto a:active {
        color: #7E6A6C  !important;
    }

</style>

@{
    List<ArquivoProblemas> arquivos = ViewBag.ArquivosGateway;
    
    if (arquivos != null)
    {  
        <div class="wrapper wrapper-content">

            @{
                string NomeGateway = ViewBag.NomeGateway_Search;
                string NomeCliente = ViewBag.NomeCliente_Search;

                if (NomeGateway.Length > 0 && NomeCliente.Length > 0)
                {
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h4>Gateway</h4><br />
                                    @NomeGateway
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h4>Cliente</h4><br />
                                    @NomeCliente
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="panel panel-title">
                                <div class="panel-heading">
                                    <h4>Número de Arquivos com Problemas (3 meses)</h4><br />
                                    @ViewBag.NumArquivosProblemas arquivos
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-title">
                                <div class="panel-heading">
                                    <h4>Número de Arquivos com Problemas (últimos 3 meses)</h4><br />
                                    @ViewBag.NumArquivosProblemas arquivos
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }

            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <h4>Arquivos da Gateway com Problemas</h4>
                        </div>
                        <div class="panel-body">
                            <table id="example" class="table table-striped table-bordered table-hover dataTables-arquivos">
                                <thead>
                                    <tr>
                                        <th>Arquivo</th>
                                        <th>Motivo do Problema</th>
                                        <th>Gateway</th>
                                        <th>Cliente</th>
                                        <th>Data e Hora do Envio</th>
                                        <th>Tamanho</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @foreach (ArquivoProblemas file in arquivos)
                                    {
                                        // copia data e hora
                                        string DataHora = String.Format("{0:g}", file.info.LastWriteTime);
                                        string DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", file.info.LastWriteTime);
                                        string Tamanho = String.Format("{0} bytes", file.info.Length);

                                        <tr>
                                            <td>@file.NomeArquivo</td>
                                            <td>@file.Motivo</td>
                                            <td>@file.NomeGateway</td>
                                            <td>@file.NomeCliente</td>
                                            <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                            <td>@Tamanho</td>
                                            <td class="link_preto">
                                                <a href="#" onclick="javascript:Download('@file.NomeArquivo','@file.info.Name');" id="BotaoDownload" title="Download"><i class="fa fa-download icones"></i></a>
                                                <a href="#" onclick="javascript:Reprocessar('@file.NomeArquivo','@file.info.Name');" id="BotaoReprocessar" title="Reprocessar"><i class="fa fa-refresh icones"></i></a>
                                                <a href="#" onclick="javascript:Excluir('@file.NomeArquivo','@file.info.Name');" id="BotaoExcluir" title="@SmartEnergy.Resources.ComumTexts.BotaoExcluir"><i class="fa fa-times icones"></i></a>
                                            </td>
                                        </tr>
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

}


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-arquivos').DataTable({
            "iDisplayLength": 15,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "numero" },
                        { "aTargets": [5], "bVisible": true, "bSortable": false, "bSearchable": false },
            ],
            "aoColumns": [
            { sWidth: "15%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "10%" },
            { sWidth: "8%" },
            { sWidth: "8%" }
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        var AchouArquivos = @Html.Raw(Json.Encode(@ViewBag.AchouArquivos));

        if (AchouArquivos == 0)
        {
            setTimeout(function () {

                swal({
                    title: "Atenção",
                    text: "Não existem arquivos desta Gateway na fila de Processamento",
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: "Fechar",
                });

            }, 100);

        }

    });


    function Download(nome, arquivo) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja fazer o download do arquivo?";

        swal({
            html: true,
            title: titulo,
            text: nome,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Download",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            //use window.location.href for redirect to download action for download the file
            window.location.href = "@Url.RouteUrl(new 
            { Controller = "Suporte", Action = "Download"})/?arquivo=" + arquivo;

        });
    };

    function Reprocessar(nome, arquivo) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja reprocessar o arquivo?";

        swal({
            html: true,
            title: titulo,
            text: nome,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Reprocessar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // reprocessar
            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/Reprocessar',
                data: { 'arquivo': arquivo, "nome": nome },
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO")
                        {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else
                        {
                            swal({
                                title: "Copiado para Recebidos",
                                text: "Aguarde o Processamento",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {


                            });
                        }

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao copiar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

    function Excluir(nome, arquivo) {
        event.stopPropagation();

        // titulo
        titulo = "Deseja excluir o arquivo?";

        swal({
            html: true,
            title: titulo,
            text: nome,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            // reprocessar
            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/Excluir',
                data: { 'arquivo': arquivo },
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO")
                        {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else
                        {
                            swal({
                                title: "Excluído com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // atualiza pagina
                                location.reload();

                            });
                        }

                    }, 100);
                },
                error: function (response) {

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao excluir!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });
    };

</script>
