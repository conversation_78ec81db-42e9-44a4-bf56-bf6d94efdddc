﻿@model IEnumerable<SmartEnergyLib.SQL.AnaliseGatewaysHistDominio>

@{
    ViewBag.Title = "Gateway" + SmartEnergy.Resources.MetasTexts.Historico;
}


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>Gateway @SmartEnergy.Resources.MetasTexts.Historico @SmartEnergy.Resources.MenuTexts.MenuLateralManutencaoGateFalha</h4>
                </div>
                <div class="panel-body">

                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h4>Gateway</h4><br />
                                    @ViewBag.NomeGateway
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h4>ID Gateway</h4><br />
                                    @ViewBag.IDGateway
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h4>@SmartEnergy.Resources.SupervisaoTexts.Total @SmartEnergy.Resources.SupervisaoTexts.Falhas - @SmartEnergy.Resources.SupervisaoTexts.Ultimos 3 @SmartEnergy.Resources.SupervisaoTexts.Meses</h4><br />
                                    @ViewBag.FalhasTotal
                                </div>
                            </div>
                        </div>
                    </div>
                    <br />
                    <br />
                    <div class="row">
                        <div class="col-lg-12">
                            <table id="dataTables-historicos" class="table table-bordered table-hover dataTables-eventos">
                                <thead>
                                    <tr>
                                        <th>Início da Falha</th>
                                        <th>Fim da Falha</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.Falha @SmartEnergy.Resources.SupervisaoTexts.Registrada</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{
                                        var listaEventosDescricao = ViewBag.listaEventosDescricao;
                                    }

                                    @foreach (var historico in Model)
                                    {
                                        string tipo_atraso = "";

                                        // tipo atraso
                                        switch (historico.TipoAtraso)
                                        {
                                            case 1: tipo_atraso = "Atraso menor que 3 dias";
                                                break;

                                            case 2: tipo_atraso = "Atraso 3 a 7 dias";
                                                break;

                                            case 3: tipo_atraso = "Atraso 7 a 23 dias";
                                                break;

                                            case 4: tipo_atraso = "Atraso maior que 23 dias";
                                                break;

                                            case 5: tipo_atraso = "Atraso no relógio da Gateway";
                                                break;

                                            case 6: tipo_atraso = "Não recebeu todos os arquivos";
                                                break;
                                        }
                                        
                                        // data e hora inicial
                                        string DataIni = "-";
                                        string DataIni_Sort = "20000101000000";

                                        // data e hora final
                                        string DataFim = "-";
                                        string DataFim_Sort = "20000101000000";
                                                                              

                                        if (historico.DataIni != null)
                                        {
                                            DataIni = String.Format("{0:g}", historico.DataIni);
                                            DataIni_Sort = String.Format("{0:yyyyMMddHHmmss}", historico.DataIni);
                                        }

                                        if (historico.DataFim != null)
                                        {
                                            
                                            
                                            if(historico.DataIni == historico.DataFim)
                                            {
                                                DataFim = String.Format("Problema não resolvido (Histórico em aberto)");                                                
                                            }
                                            else
                                            {
                                                DataFim = String.Format("{0:g}", historico.DataFim);
                                            }
                                            
                                            DataFim_Sort = String.Format("{0:yyyyMMddHHmmss}", historico.DataFim);                                       
                                        }

                                        <tr>
                                            <td><span style="display:none;">@DataIni_Sort</span>@DataIni</td>
                                            <td><span style="display:none;">@DataFim_Sort</span>@DataFim</td>
                                            <td>@tipo_atraso</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@section Styles {
    @Styles.Render("~/plugins/c3Styles")
}

@section Scripts {
    @Scripts.Render("~/plugins/d3")
    @Scripts.Render("~/plugins/c3")
    @Scripts.Render("~/plugins/dataTables")
    @Scripts.Render("~/plugins/jqueryui")

    <script type="text/javascript">

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-eventos').DataTable({
            "iDisplayLength": 12,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
            ],
            "aoColumns": [
            { sWidth: "25%" },
            { sWidth: "25%" },
            { sWidth: "50%" },
            ],
            'order': [0, 'desc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });


    </script>
}
