﻿@model SmartEnergyLib.SQL.FirmwaresDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = "Firmware";
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
    }

    .link_preto a:link {
        color: #7E6A6C !important;
    }

    .link_preto a:visited {
        color: #7E6A6C !important;
    }

    .link_preto a:hover {
        color: #a0a0a0 !important;
    }

    .link_preto a:active {
        color: #7E6A6C !important;
    }

    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        position: absolute;
        top: 70%;
        left: 50%;
        margin: 0;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Firmwares_Editar", "Suporte", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {
                @Html.Hidden("Criacao", Model.Criacao)

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>Firmware</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDFirmware == 0)
                                    {
                                        @Html.Hidden("IDFirmware", Model.IDFirmware)
                                        @Html.TextBox("Novo", "Novo Firmware", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDFirmware, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@("/Suporte/Firmwares")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                        <li class="tab-2"><a data-toggle="tab" href="#tab-2"><i class="fa fa-hdd-o"></i> Drivers</a></li>
                                    </ul>
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">

                                            <div class="panel-body">
                                                <div class="row">
                                                    <div class="form-group col-lg-6">
                                                        <div class="row">
                                                            <div class="col-lg-offset-1 col-lg-10" style="text-align:center;">
                                                                <i class="fa fa-microchip fa-5x"></i>
                                                            </div>
                                                        </div>
                                                        <br />
                                                        <div class="row">
                                                            <div class="col-lg-offset-2 col-lg-8" style="text-align:center;">
                                                                <h2>Selecionar o Firmware</h2>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-6">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <label class="control-label">&nbsp;Firmware</label><br />
                                                                <input class="form-control" disabled="disabled" id="Arquivo" name="Arquivo" type="text" value="@Model.Arquivo" />
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <div id="progress_Firmware" class="progress">
                                                                    <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                                </div>
                                                                <span class="btn btn-primary fileinput-button" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">
                                                                    <span>Selecione o Firmware</span>
                                                                    <input type="file" id="fileupload_Firmware" name="fileupload_Firmware" accept=".dat">
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <hr />
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Modelo</label>
                                                        @Html.DropDownListFor(model => model.IDTipoGateway, new SelectList(ViewBag.listaTipoGateway, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, SmartEnergy.Resources.ConfiguracaoTexts.TipoGateway),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Versao</label>
                                                        @Html.TextBoxFor(model => model.Versao, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;Biblioteca</label>
                                                        @Html.TextBoxFor(model => model.Lib, new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                    <div class="form-group col-lg-3">
                                                        <label class="control-label">&nbsp;Status</label>
                                                        @Html.DropDownListFor(model => model.IDTipoFirmwareStatus, new SelectList(ViewBag.listaFirmwareStatus, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, "Status"),
                                                            new { @class = "form-control", @disabled = "disabled" })
                                                    </div>
                                                </div>

                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-lg-3">
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <label class="control-label">&nbsp;Criação</label>
                                                                <div class="input-group date">
                                                                    <span class="input-group-addon"><i class="fa fa-calendar"></i></span>@Html.TextBoxFor(model => model.CriacaoTexto, new { @class = "form-control", @maxlength = "10" })
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <br />
                                                        <div class="row">
                                                            <div class="form-group col-lg-12">
                                                                <div class="i-checks">
                                                                    <label class="control-label" style="margin-bottom:10px;">&nbsp;Coldboot</label><br />
                                                                    @Html.CheckBoxFor(model => model.Coldboot)&nbsp;&nbsp;<span style="margin-right:14px;">Necessário Coldboot nesta versão</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group col-lg-9">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.NotasRevisao</label>
                                                        @Html.TextAreaFor(model => model.NotasRevisao, new { @class = "form-control", @maxlength = "800", @rows = "10" })
                                                    </div>
                                                </div>
                                                <br />
                                            </div>
                                        </div>

                                        <div id="tab-2" class="tab-pane">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="col-lg-12">
                                                        <label class="control-label">&nbsp;Drivers</label>
                                                        <table id="dataTables-drivers" class="table table-striped table-bordered table-hover">
                                                            <thead>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Nome</th>
                                                                    <th>Fabricante</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tfoot>
                                                                <tr>
                                                                    <th>ID</th>
                                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Nome</th>
                                                                    <th>Fabricante</th>
                                                                    <th></th>
                                                                </tr>
                                                            </tfoot>
                                                            <tbody>

                                                                @{
                                                                    List<DriversDominio> drivers = ViewBag.Drivers;

                                                                    foreach (DriversDominio driver in drivers)
                                                                    {
                                                                        <tr>
                                                                            <td>@driver.IDDriver</td>
                                                                            <td>@driver.Nome</td>
                                                                            <td>@driver.Fabricante</td>

                                                                            <td class="link_preto">

                                                                                @if (ViewBag.Permissao == PERMISSOES.ADMIN)
                                                                                {
                                                                                    <a href="#" class="confirm-delete-driver"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>
                                                                                }

                                                                            </td>
                                                                        </tr>
                                                                    }
                                                                }

                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="col-lg-4">
                                                        <button type="button" id="BotaoAdicionarDriverPadrao" class="btn btn-primary pull-left" style="color:#ffffff; width:100%;" onclick="fnAddRowDriversPadrao();">Adicionar Drivers Padrão</button>
                                                    </div>
                                                    <div class="col-lg-8">
                                                        <a id="BotaoAdicionarDriver" data-toggle="modal" href="#ModalDrivers" class="btn btn-info pull-left" style="color:#ffffff; width:100%;">Adicionar Driver</a>
                                                    </div>
                                                </div>
                                                <br />

                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal inmodal animated fadeIn" id="ModalDrivers" tabindex="-1" role="dialog" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Fechar</span></button>
                                        <h4 class="modal-title"><i class="fa fa-check-square-o"></i>&nbsp;&nbsp;Selecione o Driver</h4>
                                    </div>
                                    <div class="modal-body">

                                        <table id="dataTables-drivers2" class="table table-striped table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th><input name="select_all" value="1" type="checkbox"></th>
                                                    <th>ID</th>
                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Nome</th>
                                                    <th>Fabricante</th>
                                                </tr>
                                            </thead>
                                            <tfoot>
                                                <tr>
                                                    <th></th>
                                                    <th>ID</th>
                                                    <th>@SmartEnergy.Resources.ConfiguracaoTexts.Nome</th>
                                                    <th>Fabricante</th>
                                                </tr>
                                            </tfoot>
                                            <tbody>

                                                @{
                                                    List<DriversDominio> drivers2 = ViewBag.Drivers2;

                                                    foreach (var driver in drivers2)
                                                    {
                                                        <tr>
                                                            <td>@driver.IDDriver</td>
                                                            <td>@driver.IDDriver/td>
                                                            <td>@driver.Nome</td>
                                                            <td>@driver.Fabricante</td>
                                                        </tr>
                                                    }
                                                }

                                            </tbody>
                                        </table>

                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-white" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                        <button type="button" class="btn btn-primary" onclick="fnAddRowDrivers();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoInserir</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
    @Styles.Render("~/datetimePickerStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

    var jqXHRData;

    // listas das linhas selecionadas
    var rows_selected_drivers = [];
    var names_selected_drivers_nome = [];
    var names_selected_drivers_fabricante = [];

    var names_selected_drivers_IDFirmwareDriver = [];


    //
    // Atualiza "Select all"
    //
    function updateDataTableSelectAllCtrl(table) {
        var $table = table.table().node();
        var $chkbox_all = $('tbody input[type="checkbox"]', $table);
        var $chkbox_checked = $('tbody input[type="checkbox"]:checked', $table);
        var chkbox_select_all = $('thead input[name="select_all"]', $table).get(0);

        // se nenhum checkbox está selecionado
        if ($chkbox_checked.length === 0) {
            chkbox_select_all.checked = false;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = false;
            }

        // se todos os checkbox estão selecionados
        } else if ($chkbox_checked.length === $chkbox_all.length) {
            chkbox_select_all.checked = true;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = false;
            }

        // se algum dos checkbox está selecionado
        } else {
            chkbox_select_all.checked = true;
            if ('indeterminate' in chkbox_select_all) {
                chkbox_select_all.indeterminate = true;
            }
        }
    }


    $(document).ready(function () {

        $('#CriacaoTexto').datetimepicker({
            locale: 'pt-BR',
            format: 'DD/MM/YYYY',
            showTodayButton: true,
            allowInputToggle: true
        });

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        $.validator.addMethod('positivo', function (value) {
            return Number(value) >= 0;
        }, 'Favor especificar um número positivo.');

        $("#form").validate({
            rules: {
                Arquivo: { required: true },
                IDTipoGateway: { required: true },
                Versao: {
                    required: true,
                    numeric: true,
                    positivo: true,
                    min: 100,
                    max: 9999
                },
                IDTipoFirmwareStatus: { required: true },
                Lib: {
                    required: true,
                    numeric: true,
                    positivo: true,
                    min: 1,
                    max: 99
                },
                CriacaoTexto: { required: true }
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });


        //
        // TABELA DRIVERS
        //

        var tableDrivers = $('#dataTables-drivers').DataTable({
            "iDisplayLength": 10,
            dom: 'tp',

            "bAutoWidth": false,
            "aoColumns": [
                { sWidth: "1%" },
                { sWidth: "50%" },
                { sWidth: "40%" },
                { sWidth: "10%" },
            ],
            "columnDefs": [
                 { "targets": [0], "visible": false, "searchable": false, "orderable": false },
                 { "targets": [1], "sType": "portugues" },
                 { "targets": [2], "sType": "portugues" },
                 { "targets": [3], "searchable": false, "orderable": false },
            ],
            'order': [1, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        //
        // TABELA DRIVERS - BUSCA
        //

        // campos de busca em cada coluna na tabela
        $('#dataTables-drivers tfoot th').each(function () {
            var title = $(this).text();
            if (title.length > 0)
                $(this).html('<input type="text" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Busca ' + title + '" />');
        });

        // dataTable
        var table = $('#dataTables-drivers').DataTable();

        // aplica a busca
        table.columns().every(function () {
            var that = this;

            $('input', this.footer()).on('keyup change', function () {
                if (that.search() !== this.value) {
                    that
                        .search(this.value)
                        .draw();
                }
            });
        });

        //
        // TABELA DRIVERS - SELECAO
        //

        $('#dataTables-drivers tbody').on('click', 'tr', function () {

            if ($(this).hasClass('selected')) {

                // retira selecao atual
                $(this).removeClass('selected');

            }
            else {

                // dataTable
                var table = $('#dataTables-drivers').DataTable();

                // retira selecao atual
                table.$('tr.selected').removeClass('selected');

                // seleciona
                $(this).addClass('selected');

                // pega dados da linha selecionada
                var data = $('#dataTables-drivers').DataTable().row(this).data();
                var id = data[0];
            }
        });

        //
        // TABELA DRIVERS (MODAL)
        //

        var tableDrivers2 = $('#dataTables-drivers2').DataTable({
            "iDisplayLength": 6,
            dom: 'tp',

            'order': [2, 'asc'],
            "bAutoWidth": false,
            "aoColumns": [
            { sWidth: "5%" },
            { sWidth: "20%" },
            { sWidth: "20%" },
            { sWidth: "30%" },
            ],

            'columnDefs': [{
                'targets': 0,
                'searchable': false,
                'orderable': false,
                'className': 'dt-center',
                'render': function (data, type, full, meta) {
                    return '<input type="checkbox" name="checkbox_modal_medicoes">';
                },
            },
            {
                'targets': 1,
                'visible': false,
                'searchable': false,
                'orderable': false,
            }],

            'rowCallback': function (row, data, dataIndex) {

                // pega ID
                var rowId = data[0];

                // verifica se ID esta na lista de selecionados
                if ($.inArray(rowId, rows_selected_drivers) !== -1) {
                    $(row).find('input[type="checkbox"]').prop('checked', true);
                    $(row).addClass('selected');
                }
            },

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }
        });

        // manipula checkbox
        $('#dataTables-drivers2 tbody').on('click', 'input[type="checkbox"]', function (e) {
            var $row = $(this).closest('tr');

            // pega dados da linha
            var data = tableDrivers2.row($row).data();

            // pega ID e campos
            var rowId = data[0];
            var name_nome = data[2];
            var name_fabricante = data[3];

            // verifica se ID esta na lista de selecionados
            var index = $.inArray(rowId, rows_selected_drivers);

            // caso checkbox está selecionado e o ID não está na lista de selecionados
            if (this.checked && index === -1) {
                rows_selected_drivers.push(rowId);
                names_selected_drivers_nome.push(name_nome);
                names_selected_drivers_fabricante.push(name_fabricante);

                names_selected_drivers_IDFirmwareDriver.push(0);

            // senão, se checkbox não está selecionado e o ID está na lista de selecionados
            } else if (!this.checked && index !== -1) {
                rows_selected_drivers.splice(index, 1);
                names_selected_drivers_nome.splice(index, 1);
                names_selected_drivers_fabricante.splice(index, 1);

                names_selected_drivers_IDFirmwareDriver.splice(index, 1);
            }

            if (this.checked) {
                $row.addClass('selected');
            } else {
                $row.removeClass('selected');
            }

            // atualiza status do "Select all"
            updateDataTableSelectAllCtrl(tableDrivers2);

            // previne evento clique de se propagar
            e.stopPropagation();
        });

        // manipula click na tabela
        $('#dataTables-drivers2').on('click', 'tbody td, thead th:first-child', function (e) {

            $(this).parent().find('input[type="checkbox"]').trigger('click');
        });

        // manipula click no "Select all"
        $('thead input[name="select_all"]', tableDrivers2.table().container()).on('click', function (e) {
            if (this.checked) {
                $('tbody input[type="checkbox"]:not(:checked)', tableDrivers2.table().container()).trigger('click');
            } else {
                $('tbody input[type="checkbox"]:checked', tableDrivers2.table().container()).trigger('click');
            }

            // previne evento clique de se propagar
            e.stopPropagation();
        });

        // manipula evento 'draw'
        tableDrivers2.on('draw', function () {
            // atualiza status do "Select all"
            updateDataTableSelectAllCtrl(tableDrivers2);
        });

        // campos de busca em cada coluna na tabela
        $('#dataTables-drivers2 tfoot th').each(function () {
            var title = $(this).text();
            if (title.length > 0)
                $(this).html('<input type="text" placeholder="@SmartEnergy.Resources.UsuarioPerfilTexts.Busca ' + title + '" />');
        });

        // dataTable
        var table = $('#dataTables-drivers2').DataTable();

        // aplica a busca
        table.columns().every(function () {
            var that = this;

            $('input', this.footer()).on('keyup change', function () {
                if (that.search() !== this.value) {
                    that
                        .search(this.value)
                        .draw();
                }
            });
        });

        //
        // UPDATE DO FIRMWARE
        //

        // upload do firmware
        $('#fileupload_Firmware').fileupload({
            url: '/Suporte/Firmwares_UploadFile',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {
                if (data.result.isUploaded) {

                    setTimeout(function () {

                        $('#progress_Firmware .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        document.getElementById('Arquivo').value = data.result.arquivo;
                        document.getElementById('IDTipoGateway').value = data.result.IDTipoGateway;
                        document.getElementById('Versao').value = data.result.versao;
                        document.getElementById('Lib').value = data.result.lib;
                        document.getElementById('IDTipoFirmwareStatus').value = data.result.status;
                        document.getElementById('Criacao').value = data.result.criacao;

                        if (data.result.arquivo.length > 0) {
                            $("#BotaoSalvar").attr('disabled', false);
                        }

                        swal({
                            title: "Enviado com sucesso",
                            text: data.result.message,
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });

                    }, 1000);
                }
                else {
                    setTimeout(function () {

                        swal({
                            title: "Erro no envio",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            $('#progress_Firmware .progress-bar').css(
                            'width',
                            0 + '%'
                            );
                        });

                    }, 1000);
                }

            },
            fail: function (event, data) {
                if (data.files[0].error) {

                    swal({
                        title: "Erro no envio",
                        text: data.files[0].error,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                    });
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress_Firmware .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');



        // desabilita campos
        disableAll();

        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });
    });

    //
    // DESABILITA CAMPOS
    //

    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:     // 0 - permissao de Admin: ve e escreve em tudo

                var arquivo = document.getElementById('Arquivo').value;

                if (arquivo.length > 0) {
                    $("#BotaoSalvar").attr('disabled', false);
                }

                $("#fileupload_Firmware").attr('disabled', false);
                $("#BotaoAdicionarDriverPadrao").attr('disabled', true);
                $("#BotaoAdicionarDriver").attr('disabled', true);
                break;

            default:
            case PERMISSOES_SUPORTE:   // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte
            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                $("#BotaoSalvar").attr('disabled', true);
                $("#fileupload_Firmware").attr('disabled', true);
                $("#BotaoAdicionarDriverPadrao").attr('disabled', true);
                $("#BotaoAdicionarDriver").attr('disabled', true);
                break;
        }
    }

    //
    // BOTAO ADICIONA DRIVERS
    //

    function fnAddRowDrivers() {

        // tabela
        var tableDrivers = $('#dataTables-drivers').dataTable();
        var row_countDrivers = tableDrivers.fnGetData().length;
        var linhasDrivers = tableDrivers.fnGetData();

        // percorre lista de selecionados
        $.each(rows_selected_drivers, function (index, rowId) {

            // procura na tabela de utilizados se existe iD
            var achou = false;

            for (var j = 0; j < row_countDrivers; j++) {

                if (linhasDrivers[j][0] == rowId) {
                    achou = true;
                }
            }

            // insere na tabela se nao achou
            if (achou == false) {
                $('#dataTables-drivers').dataTable().fnAddData([
                    rowId,
                    names_selected_drivers_nome[index],
                    names_selected_drivers_fabricante[index],
                    '<a href="#" class="confirm-delete-driver link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>']);
            }
        });

        // apaga arrays
        rows_selected_drivers = [];
        names_selected_drivers_nome = [];
        names_selected_drivers_fabricante = [];

        names_selected_drivers_IDFirmwareDriver = [];

        // desmarca checkbox
        var boxes = document.getElementsByName("checkbox_modal_drivers");
        for (var i = 0; i < boxes.length; i++)
            boxes[i].checked = false;

        var chkbox_select_all = $('thead input[name="select_all"]', $('#dataTables-drivers2')).get(0);
        chkbox_select_all.indeterminate = false;
        chkbox_select_all.checked = false;

        // remove da lista
        $('#dataTables-drivers2').dataTable().$('tr.selected').empty();
        $('#dataTables-drivers2').dataTable().draw();

        // desenha tabela
        $('#dataTables-drivers').dataTable().draw();
    }

    function fnAddRowDriversPadrao() {

        // lê os drivers que são padrão
        $.getJSON('/Suporte/ObterDriversPadrao', { }, function (data) {

            // tabela
            var tableDrivers = $('#dataTables-drivers').dataTable();
            var row_countDrivers = tableDrivers.fnGetData().length;
            var linhasDrivers = tableDrivers.fnGetData();

            var tableDrivers2 = $('#dataTables-drivers2').dataTable();
            var row_countDrivers2 = tableDrivers2.fnGetData().length;
            var linhasDrivers2 = tableDrivers2.fnGetData();

            // percorre lista de drivers padrão
            for (var i = 0; i < data.length; i++) {

                // IDDriver
                var rowId = data[i].IDDriver;

                // procura na tabela de utilizados se existe iD
                var achou = false;

                for (var j = 0; j < row_countDrivers; j++) {

                    if (linhasDrivers[j][0] == rowId) {
                        achou = true;
                    }
                }

                // insere na tabela se nao achou
                if (achou == false) {
                    $('#dataTables-drivers').dataTable().fnAddData([
                        rowId,
                        data[i].Nome,
                        data[i].Fabricante,
                        '<a href="#" class="confirm-delete-driver link_preto"><i class="fa fa-times icones" title="@SmartEnergy.Resources.UsuarioPerfilTexts.Excluir"></i></a>']);
                }

                // procura na tabela de não utilizados se existe iD
                for (var j = 0; j < row_countDrivers2; j++) {

                    if (linhasDrivers2[j][0] == rowId) {
                        //$('#dataTables-drivers2').dataTable().row(j).remove().draw();
                        $('#dataTables-drivers2').dataTable().fnDeleteRow(j);
                    }
                }
            }
        });

        // apaga arrays
        rows_selected_drivers = [];
        names_selected_drivers_nome = [];
        names_selected_drivers_fabricante = [];

        names_selected_drivers_IDFirmwareDriver = [];

        // desmarca checkbox
        var boxes = document.getElementsByName("checkbox_modal_drivers");
        for (var i = 0; i < boxes.length; i++)
            boxes[i].checked = false;

        var chkbox_select_all = $('thead input[name="select_all"]', $('#dataTables-drivers2')).get(0);
        chkbox_select_all.indeterminate = false;
        chkbox_select_all.checked = false;



        // desenha tabela
        $('#dataTables-drivers').dataTable().draw();
    }

    //
    // BOTAO APAGAR DRIVER
    //

    $('#dataTables-drivers').on('click', '.confirm-delete-driver', function (e) {
        e.preventDefault();

        // linha
        var row = $(this).closest('tr');

        // le dados da linha
        var data = $('#dataTables-drivers').DataTable().row(row).data();

        var rowId = data[0];
        var nome = data[1];
        var fabricante = data[2];

        // titulo
        titulo = "Deseja excluir o driver da lista?";

        swal({
            html: true,
            title: titulo,
            text: "Esta operação não poderá ser desfeita.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#18a689",
            confirmButtonText: "Excluir",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {

            setTimeout(function () {

                // insere no modal
                $('#dataTables-drivers2').dataTable().fnAddData([
                    rowId,
                    rowId,
                    nome,
                    fabricante]);

                // apaga linha solicitada
                $('#dataTables-drivers').dataTable().fnDeleteRow(row);

                // desenha tabela
                $('#dataTables-drivers').dataTable().draw();

                // desenha tabela
                $('#dataTables-drivers2').dataTable().draw();


            }, 100);

        });

    });



    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar() {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;

        // lista de drivers
        var oTable = $('#dataTables-drivers').dataTable();
        var rows = oTable.fnSettings().aoData;
        var IDFirmware = document.getElementById('IDFirmware').value;
        var IDDriver = 0;
        var dataArray = [];

        // percorre tabela
        $.each(rows, function (i, val) {

            // pega valores
            IDDriver = parseInt(val._aData[0]);

            dataArray.push({
                "IDFirmware": IDFirmware,
                "IDDriver": IDDriver
            });
        });

        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            // constroi estrutura com FORM e lista de medicoes
            data = { 'fw': $form.serializeObject(), 'fwDrvs': dataArray };
            data2 = JSON.stringify(data);

            $.ajax({
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                url: '/Suporte/Firmwares_Salvar',
                data: data2,
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de firmwares
                                var url = '/Suporte/Firmwares';
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });

    };

    </script>
}
