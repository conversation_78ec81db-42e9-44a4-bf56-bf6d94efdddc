﻿@model SmartEnergyLib.SQL.DriversDominio

@using System.Globalization
@using SmartEnergyLib.SQL
@using SmartEnergyLib.Declaracoes

@{
    ViewBag.Title = "Driver";
}

<style>
    .overlay_aguarde {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.45);
        z-index: 9999;
        color: white;
        display: inline-block;
        border-radius: 6px;
        margin-bottom: 20px;
        margin-left: 14px;
        margin-right: 14px;
    }

    .sk-spinner-wave div {
        background-color: #ffffff !important;
    }

    .spinner-aguarde {
        position: absolute;
        top: 70%;
        left: 50%;
        margin: 0;
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
    }
</style>


<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">

            @using (Html.BeginForm("Drivers_Editar", "Suporte", new { ReturnUrl = ViewBag.ReturnUrl }, FormMethod.Post, new { id = "form", role = "form" }))
            {

                <div class="panel panel-title">
                    <div class="panel-heading">
                        <h4>Driver</h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group col-lg-2">
                                <label class="control-label">&nbsp;ID</label>

                                @{
                                    if (Model.IDDriver == 0)
                                    {
                                        @Html.Hidden("IDDriver", Model.IDDriver)
                                        @Html.TextBox("Novo", "Novo Driver", new { @class = "form-control", @disabled = "disabled" })
                                    }
                                    else
                                    {
                                        @Html.TextBoxFor(model => model.IDDriver, new { @class = "form-control", @disabled = "disabled" })
                                    }
                                }

                            </div>
                            <div class="col-lg-offset-4 col-lg-6">
                                <div class="ibox-tools">
                                    <a href='@("/Suporte/Drivers")' class="btn btn-default" style="color:#000000;">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</a>
                                    <button type="button" class="btn btn-primary" id="BotaoSalvar" onclick="Salvar();" disabled="">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                </div>
                            </div>
                        </div>
                        <br />
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="tabs-container">
                                    <ul class="nav nav-tabs">
                                        <li class="active"><a data-toggle="tab" href="#tab-1"><i class="fa fa-cogs"></i> @SmartEnergy.Resources.ConfiguracaoTexts.Geral</a></li>
                                    </ul>
                                    <div class="tab-content">

                                        <div id="tab-1" class="tab-pane active">
                                            <div class="panel-body">

                                                <div class="row">
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Nome</label>
                                                        @Html.TextBoxFor(model => model.Nome, new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;Fabricante</label>
                                                        @Html.DropDownListFor(model => model.IDTipoFabricante, new SelectList(ViewBag.listaFabricantes, "ID", "Descricao"),
                                                            string.Format("{0} {1}", SmartEnergy.Resources.ConfiguracaoTexts.Selecione, "o Fabricante"),
                                                            new { @class = "form-control" })
                                                    </div>
                                                    <div class="form-group col-lg-4">
                                                        <label class="control-label">&nbsp;Endereço do Driver</label>
                                                        @Html.TextBoxFor(model => model.drvAd, new { @class = "form-control", @maxlength = "5" })
                                                    </div>
                                                </div>
                                                <br />
                                                <div class="row">
                                                    <div class="form-group col-sm-8">
                                                        <label class="control-label">&nbsp;@SmartEnergy.Resources.ConfiguracaoTexts.Descricao</label>
                                                        @Html.TextAreaFor(model => model.Descricao, new { @class = "form-control", @maxlength = "400", @rows = "5" })
                                                    </div>
                                                    <div class="form-group col-sm-4">
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Manual</label><br />
                                                                @Html.TextBoxFor(model => model.Manual1, new { @class = "form-control" })
                                                            </div>
                                                            <div class="form-group col-lg-6" id="BotaoUploadDIV">
                                                                <div id="progress_Manual1" class="progress">
                                                                    <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                                </div>
                                                                <span id="BotaoUpload" class="btn btn-primary fileinput-button" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">
                                                                    <span>Selecione o Manual</span>
                                                                    <input type="file" id="fileupload_Manual1" name="fileupload_Manual1" accept=".pdf">
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col-lg-6">
                                                                <label class="control-label">&nbsp;Manual</label><br />
                                                                @Html.TextBoxFor(model => model.Manual2, new { @class = "form-control" })
                                                            </div>
                                                            <div class="form-group col-lg-6" id="BotaoUploadDIV">
                                                                <div id="progress_Manual2" class="progress">
                                                                    <div class="progress-bar progress-bar-success bar" style="width: 0%; height:20px;"></div>
                                                                </div>
                                                                <span id="BotaoUpload" class="btn btn-primary fileinput-button" style="height:30px; width:100%; margin-top:-18px; padding-top:4px;">
                                                                    <span>Selecione o Manual</span>
                                                                    <input type="file" id="fileupload_Manual2" name="fileupload_Manual2" accept=".pdf">
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <hr />
                                                <div class="row">
                                                    <div class="col-lg-12">
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="i-checks">
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">Tensão<br />Fase-Fase</label><br />
                                                            @Html.CheckBoxFor(model => model.Vff)&nbsp;&nbsp;<span style="margin-right:14px;">FF</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.V12)&nbsp;&nbsp;<span style="margin-right:14px;">F12</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.V23)&nbsp;&nbsp;<span style="margin-right:14px;">F23</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.V31)&nbsp;&nbsp;<span style="margin-right:14px;">F31</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">Tensão<br />Fase-Neutro</label><br />
                                                            @Html.CheckBoxFor(model => model.Vfn)&nbsp;&nbsp;<span style="margin-right:14px;">FN</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.V1n)&nbsp;&nbsp;<span style="margin-right:14px;">F1N</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.V2n)&nbsp;&nbsp;<span style="margin-right:14px;">F2N</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.V3n)&nbsp;&nbsp;<span style="margin-right:14px;">F3N</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">&nbsp;<br />Corrente</label><br />
                                                            @Html.CheckBoxFor(model => model.C)&nbsp;&nbsp;<span style="margin-right:14px;">Total</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.C1)&nbsp;&nbsp;<span style="margin-right:14px;">F1</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.C2)&nbsp;&nbsp;<span style="margin-right:14px;">F2</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.C3)&nbsp;&nbsp;<span style="margin-right:14px;">F3</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">Potência<br />Ativa</label><br />
                                                            @Html.CheckBoxFor(model => model.P)&nbsp;&nbsp;<span style="margin-right:14px;">Total</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.P1)&nbsp;&nbsp;<span style="margin-right:14px;">F1</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.P2)&nbsp;&nbsp;<span style="margin-right:14px;">F2</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.P3)&nbsp;&nbsp;<span style="margin-right:14px;">F3</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">Potência<br />Reativa</label><br />
                                                            @Html.CheckBoxFor(model => model.Q)&nbsp;&nbsp;<span style="margin-right:14px;">Total</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.Q1)&nbsp;&nbsp;<span style="margin-right:14px;">F1</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.Q2)&nbsp;&nbsp;<span style="margin-right:14px;">F2</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.Q3)&nbsp;&nbsp;<span style="margin-right:14px;">F3</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">Potência<br />Aparente</label><br />
                                                            @Html.CheckBoxFor(model => model.S)&nbsp;&nbsp;<span style="margin-right:14px;">Total</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.S1)&nbsp;&nbsp;<span style="margin-right:14px;">Fase 1</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.S2)&nbsp;&nbsp;<span style="margin-right:14px;">Fase 2</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.S3)&nbsp;&nbsp;<span style="margin-right:14px;">Fase 3</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">Fator de<br />Potência</label><br />
                                                            @Html.CheckBoxFor(model => model.FP)&nbsp;&nbsp;<span style="margin-right:14px;">Total</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.FP1)&nbsp;&nbsp;<span style="margin-right:14px;">F1</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.FP2)&nbsp;&nbsp;<span style="margin-right:14px;">F2</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.FP3)&nbsp;&nbsp;<span style="margin-right:14px;">F3</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">THD<br />Tensão</label><br />
                                                            @Html.CheckBoxFor(model => model.THDv)&nbsp;&nbsp;<span style="margin-right:14px;">Total</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.THDv1)&nbsp;&nbsp;<span style="margin-right:14px;">F1</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.THDv2)&nbsp;&nbsp;<span style="margin-right:14px;">F2</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.THDv3)&nbsp;&nbsp;<span style="margin-right:14px;">F3</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">THD<br />Corrente</label><br />
                                                            @Html.CheckBoxFor(model => model.THDc)&nbsp;&nbsp;<span style="margin-right:14px;">Total</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.THDc1)&nbsp;&nbsp;<span style="margin-right:14px;">F1</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.THDc2)&nbsp;&nbsp;<span style="margin-right:14px;">F2</span><br /><br />
                                                            @Html.CheckBoxFor(model => model.THDc3)&nbsp;&nbsp;<span style="margin-right:14px;">F3</span><br /><br />
                                                        </div>
                                                        <div class="col-lg-1">
                                                            <label class="control-label" style="margin-bottom:14px;">&nbsp;<br />Frequência</label><br />
                                                            @Html.CheckBoxFor(model => model.Hz)&nbsp;&nbsp;<span style="margin-right:14px;">Hz</span>
                                                        </div>
                                                        <div class="col-lg-2">
                                                            <label class="control-label" style="margin-bottom:14px;">&nbsp;<br />Ciclômetro</label><br />
                                                            @Html.CheckBoxFor(model => model.Ciclometro)&nbsp;&nbsp;<span style="margin-right:14px;">Ciclômetro</span>
                                                        </div>

                                                    </div>
                                                </div>
                                                <br />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overlay_aguarde" style="display: none">
                        <div class="spinner-aguarde">
                            <div class="sk-spinner sk-spinner-wandering-cubes">
                                <div class="sk-spinner sk-spinner-wave">
                                    <div class="sk-rect1 text-ligh"></div>
                                    <div class="sk-rect2"></div>
                                    <div class="sk-rect3"></div>
                                    <div class="sk-rect4"></div>
                                    <div class="sk-rect5"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            }

        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/jQuery.FileUpload/FileUploadStyles")
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/plugins/jasnyBootstrapStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jeditable")
    @Scripts.Render("~/plugins/validate")
    @Scripts.Render("~/jQuery.FileUpload/FileUpload")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/plugins/jasnyBootstrap")
    @Scripts.Render("~/plugins/iCheck")
    @Scripts.Render("~/declaracoes/Constants")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    var jqXHRData;

    $(document).ready(function () {

        // classe iChecks
        $('.i-checks').iCheck({
            checkboxClass: 'icheckbox_square-green',
            radioClass: 'iradio_square-green',
        });

        //
        // VALIDACAO DOS CAMPOS
        //

        jQuery.validator.addMethod("alphanumeric", function (value, element) {
            return this.optional(element) || /^[\'\.\^\~\´\`\\áÁ\\àÀ\\ãÃ\\âÂ\\éÉ\\èÈ\\êÊ\\íÍ\\ìÌ\\óÓ\\òÒ\\õÕ\\ôÔ\\úÚ\\ùÙ\\çÇaA-zZ0-9\s.\&\+\-\_\(\)," "]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.alphanumeric");

        jQuery.validator.addMethod("numeric", function (value, element) {
            return this.optional(element) || /^[\\0-9\-,]+$/i.test(value);
        }, "@SmartEnergy.Resources.ValidateTexts.numeric");

        $("#form").validate({
            rules: {
                Nome: {
                    required: true,
                    alphanumeric: true,
                    minlength: 3,
                    maxlength: 50
                },
                IDTipoFabricante: { required: true },
                drvAd: {
                    numeric: true,
                    positivo: true,
                    min: 0,
                    max: 65000
                },
            },
            highlight: function (element) {
                $(element).closest('.control-group').removeClass('success').addClass('error');
            },
            unhighlight: function (element) {
                $(element).closest('.control-group').addClass('valid success').removeClass('error');
            }
        });

        // desabilita campos
        disableAll();


        // manual 1
        $('#fileupload_Manual1').fileupload({
            url: '/Suporte/UploadFile_Manual1',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {

                if (data.result.isUploaded) {

                    setTimeout(function () {

                        $('#progress_Manual1 .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        document.getElementById('Manual1').value = data.result.arquivo;

                        swal({
                            title: "Enviado com sucesso",
                            text: data.result.message,
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });

                    }, 1000);
                }
                else {
                    setTimeout(function () {

                        swal({
                            title: "Erro no envio",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            $('#progress_Manual1 .progress-bar').css(
                            'width',
                            0 + '%'
                            );

                        });

                    }, 1000);
                }

            },
            fail: function (event, data) {
                if (data.files[0].error) {

                    swal({
                        title: "Erro no envio",
                        text: data.files[0].error,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        $('#progress_Manual1 .progress-bar').css(
                        'width',
                        0 + '%'
                        );

                    });
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress_Manual1 .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');

        // manual 2
        $('#fileupload_Manual2').fileupload({
            url: '/Suporte/UploadFile_Manual2',
            dataType: 'json',
            add: function (e, data) {
                jqXHRData = data;
                jqXHRData.submit();
            },
            done: function (e, data) {

                if (data.result.isUploaded) {

                    setTimeout(function () {

                        $('#progress_Manual2 .progress-bar').css(
                                                'width',
                                                0 + '%'
                                            );

                        document.getElementById('Manual2').value = data.result.arquivo;

                        swal({
                            title: "Enviado com sucesso",
                            text: data.result.message,
                            type: "success",
                            confirmButtonColor: "#18a689",
                            confirmButtonText: "Fechar",
                        }, function () {

                        });

                    }, 1000);
                }
                else {
                    setTimeout(function () {

                        swal({
                            title: "Erro no envio",
                            text: data.result.message,
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        }, function () {

                            $('#progress_Manual2 .progress-bar').css(
                            'width',
                            0 + '%'
                            );

                        });

                    }, 1000);
                }

            },
            fail: function (event, data) {
                if (data.files[0].error) {

                    swal({
                        title: "Erro no envio",
                        text: data.files[0].error,
                        type: "warning",
                        confirmButtonColor: "#f8ac59",
                        confirmButtonText: "Fechar",
                    }, function () {

                        $('#progress_Manual2 .progress-bar').css(
                        'width',
                        0 + '%'
                        );

                    });
                }
            },
            progressall: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                $('#progress_Manual2 .progress-bar').css(
                    'width',
                    progress + '%'
                );
            }
        }).prop('disabled', !$.support.fileInput)
                .parent().addClass($.support.fileInput ? undefined : 'disabled');


        $('form input').on('keypress', function (e) {
            return e.which !== 13;
        });








    });


    function disableAll() {

        var permissao = Math.ceil(@ViewBag.Permissao);

        // verifica permissao
        switch (permissao) {
            case PERMISSOES_ADMIN:     // 0 - permissao de Admin: ve e escreve em tudo
            case PERMISSOES_SUPORTE:   // 4 - permissao de Suporte: escreve tudo mas nao pode excluir
            case PERMISSOES_PRODUCAO:   // 3 - permissao de Producao: ve tudo e escreve parte (o que é interesse da producao)

                $("#BotaoSalvar").attr('disabled', false);
                break;

            default:
            case PERMISSOES_GERENTE:    // 1 - permissao de Gerente: ve tudo e escreve parte
            case PERMISSOES_OPERADOR:   // 2 - permissao de Operador: ve tudo e nao pode escrever
            case PERMISSOES_SUPER_CONSULTOR:    // 6 - permissao de Super Consultor (CPFL): ve tudo e escreve boa parte
            case PERMISSOES_CONSULTOR:  // 5 - permissao de Consultor

                $("#BotaoSalvar").attr('disabled', true);
                break;
        }
    }


    //
    // BOTAO SALVAR (SUBMIT)
    //

    $.fn.serializeObject = function () {
        var o = {};
        var a = this.serializeArray();
        $.each(a, function () {
            if (o[this.name] !== undefined) {
                if (!o[this.name].push) {
                    o[this.name] = [o[this.name]];
                }
                o[this.name].push(this.value || '');
            } else {
                o[this.name] = this.value || '';
            }
        });
        return o;
    };

    function Salvar() {

        var $form = $('#form');

        // apresenta primeiro tab que tiver erro
        var IsTabValid = true;
        var TabInvalid = 1;

        $(".tab-content").find("div.tab-pane").each(function (index, tab) {
            var id = $(tab).attr("id");
            $('a[href="#' + id + '"]').tab('show');

            IsTabValid = $("#form").valid();

            if (!IsTabValid) {

                TabInvalid = id;
                return false;
            }
        });

        // apresenta primeira tab ou com erro
        $('a[href="#tab-' + TabInvalid + '"]').tab('show');

        // verifica se entradas sao validas
        if (!IsTabValid) return false;

        swal({
            title: "Deseja salvar?",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#1ab394",
            confirmButtonText: "Salvar",
            cancelButtonText: "Cancelar",
            closeOnConfirm: true
        }, function () {
            event.stopPropagation();

            // aguarde
            $('.overlay_aguarde').toggle();

            // retiro campos desabilitados, pois nao envia via POST
            $(":disabled", $('#form')).removeAttr("disabled");

            $.ajax({
                url: '/Suporte/Drivers_Salvar',
                data: $form.serialize(),
                type: 'POST',
                success: function (data) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        // verifica se erro
                        if (data.status == "ERRO") {
                            swal({
                                html: true,
                                title: "Erro",
                                text: data.erro,
                                type: "warning",
                                confirmButtonColor: "#f8ac59",
                                confirmButtonText: "Fechar",
                            }, function () {

                            });
                        }
                        else {
                            swal({
                                title: "Salvo com sucesso",
                                type: "success",
                                confirmButtonColor: "#18a689",
                                confirmButtonText: "Fechar",
                            }, function () {

                                // redireciona para pagina lista de drivers
                                var url = '/Suporte/Drivers';
                                window.location.href = url;
                            });
                        }

                    }, 100);

                },
                error: function (xhr, status, error) {

                    // fim
                    $('.overlay_aguarde').toggle();

                    setTimeout(function () {

                        swal({
                            title: "Erro",
                            text: "Erro ao salvar!",
                            type: "warning",
                            confirmButtonColor: "#f8ac59",
                            confirmButtonText: "Fechar",
                        });

                    }, 100);

                }
            });
        });

    };

    </script>
}
