﻿@using SmartEnergyLib.SQL

@{
    List<FileInfo> arquivos = ViewBag.ArquivosGateway;
    
    if (arquivos != null)
    {  
        <div class="wrapper wrapper-content">

            @{
                string NomeGateway = ViewBag.NomeGateway_Search;
                string NomeCliente = ViewBag.NomeCliente_Search;

                if (NomeGateway.Length > 0 && NomeCliente.Length > 0)
                {
                    <div class="row">
                        <div class="col-lg-4">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h4>Gateway</h4><br />
                                    @NomeGateway
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="panel panel-success">
                                <div class="panel-heading">
                                    <h4>Cliente</h4><br />
                                    @NomeCliente
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="panel panel-title">
                                <div class="panel-heading">
                                    <h4>Fila de Processamento</h4><br />
                                    @ViewBag.NumArquivosRecebidos arquivos
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="panel panel-title">
                                <div class="panel-heading">
                                    <h4>Fila de Processamento</h4><br />
                                    @ViewBag.NumArquivosRecebidos arquivos
                                </div>
                            </div>
                        </div>
                    </div>
                    
                }
            }

            <div class="row">
                <div class="col-lg-12">
                    <div class="panel panel-title">
                        <div class="panel-heading">
                            <h4>Arquivos da Gateway na Fila</h4>
                        </div>
                        <div class="panel-body">
                            <table id="example" class="table table-bordered table-hover dataTables-arquivos">
                                <thead>
                                    <tr>
                                        <th>Arquivo</th>
                                        <th>Data e Hora do Envio</th>
                                        <th>Tamanho</th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @foreach (FileInfo file in arquivos)
                                    {
                                        // copia data e hora
                                        string DataHora = String.Format("{0:g}", file.LastWriteTime);
                                        string DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", file.LastWriteTime);
                                        string Tamanho = String.Format("{0} bytes", file.Length);
                                        
                                        <tr>
                                            <td>@file.Name</td>
                                            <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>                             
                                            <td>@Tamanho</td>
                                        </tr>
                                    }

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

}


<script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-arquivos').DataTable({
            "iDisplayLength": 15,
            dom: 'ftp',

            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "numero" }
            ],
            "aoColumns": [
            { sWidth: "40%" },
            { sWidth: "30%" },
            { sWidth: "30%" }
            ],
            'order': [0, 'asc'],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                    "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

        });

        var AchouArquivos = @Html.Raw(Json.Encode(@ViewBag.AchouArquivos));

        if (AchouArquivos == 0)
        {
            setTimeout(function () {

                swal({
                    title: "Atenção",
                    text: "Não existem arquivos desta Gateway na fila de Processamento",
                    type: "warning",
                    confirmButtonColor: "#f8ac59",
                    confirmButtonText: "Fechar",
                });

            }, 100);

        }

    });


</script>
