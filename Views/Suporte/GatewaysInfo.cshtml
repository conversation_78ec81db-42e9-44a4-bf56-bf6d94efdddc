﻿@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = "Gateways " + SmartEnergy.Resources.MenuTexts.MenuLateralManutencaoGateInfo;
}

<head>
    <title>Gateways @SmartEnergy.Resources.MenuTexts.MenuLateralManutencaoGateInfo</title>
    <style>

        a:link {
            color: #676A6C;
        }

        a:hover {
            color: #AFB0B1;
        }


        .tooltip-inner {
            max-width: 350px;
            /* If max-width does not work, try using width instead */
            width: 350px; 
            white-space:pre-wrap;
            text-align:left;
    
        }

    </style>
</head>

<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>Gateways @SmartEnergy.Resources.MenuTexts.MenuLateralManutencaoGateInfo</h4>
                </div>
                <div class="panel-body">
                    <table class="table table-striped table-bordered table-hover dataTables-gateways">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Gateway</th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                <th>IP</th>
                                <th>ICC</th>
                                <th><i class="fa fa-signal icones"></i> @SmartEnergy.Resources.SupervisaoTexts.Sinal</th>
                                <th>@SmartEnergy.Resources.SupervisaoTexts.Operadora</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>

                            @{
                                var gateways = ViewBag.SupervListInfo;

                                if (gateways != null)
                                {
                                    foreach (var gateway in @ViewBag.SupervListInfo)
                                    {
                                        // data e hora atualizacao
                                        string DataHora = "-";
                                        string DataHora_Sort = "20000101000000";

                                        if (gateway.DataHora != null)
                                        {
                                            DataHora = String.Format("{0:G}", gateway.DataHora);
                                            DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", gateway.DataHora);
                                        }
                                        
                                        int IDTipoAcesso = ViewBag._IDTipoAcesso;
                                        string link_gateway = "";
                                        
                                        if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                        {
                                            link_gateway = "/Suporte/GatewayInfo?IDGateway=" + @gateway.IDGateway.ToString();
                                        }
                                        
                                        <tr style="cursor:pointer;" onclick="location.href='@link_gateway'">

                                            <td>@gateway.IDGateway</td>
                                            <td>@gateway.Nome</td>
                                            <td><span style="display:none;">@DataHora_Sort</span>@DataHora</td>
                                            <td>@gateway.IP</td>
                                            <td><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@gateway.MOD">@gateway.ICC</span></td>

                                            @{
                                                int sinal = gateway.Sinal;
                                                string SinalTexto = "Sinal Fraco";
                                                string SinalCor = "red";
                                                string sinalStr = "-";

                                                if (sinal >= 38 && sinal < 50)
                                                {
                                                    SinalTexto = "Sinal Médio Fraco";
                                                    SinalCor = "orange";
                                                }

                                                if (sinal >= 50 && sinal < 68)
                                                {
                                                    SinalTexto = "Sinal Médio";
                                                    SinalCor = "orange";
                                                }

                                                if (sinal >= 68 && sinal <= 100)
                                                {
                                                    SinalTexto = "Sinal Forte";
                                                    SinalCor = "green";
                                                }

                                                if (sinal >= 100)
                                                {
                                                    sinal = 999;
                                                }

                                                if (sinal > 0)
                                                {
                                                    sinalStr = string.Format("{0}%", gateway.Sinal);
                                                }
                                            }

                                            <td>
                                                <span style="display:none;">@string.Format("{000}", sinal)</span>
                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@SinalTexto"><font color="@SinalCor"><b>@sinalStr</b></font></span>
                                            </td>

                                            @{
                                                string operadora = "-";

                                                if (gateway.OP != null)
                                                {
                                                    if (gateway.OP.Length > 0)
                                                    {
                                                        operadora = gateway.OP;
                                                    }
                                                }
                                            }

                                            <td>
                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom">@operadora</span>
                                            </td>
                                            @{
                                                string Status_Sort = "";

                                                //
                                                // STATUS UPLOAD ARQUIVOS
                                                //

                                                // TipoAtraso atualizacao
                                                int TipoAtraso = 0;


                                                // pego IDHist e tipo atraso
                                                string[] separa_Status = gateway.Status.Split('/');
                                                string[] separa_TipoAtraso = separa_Status[0].Split(' ');

                                                if (separa_TipoAtraso.Length == 2)
                                                {
                                                    TipoAtraso = int.Parse(separa_TipoAtraso[1]);
                                                }
                                                                
                                                string cor_upload = "green";

                                                Status_Sort = "Z";

                                                if (TipoAtraso > 0)
                                                {
                                                    Status_Sort = "A";

                                                    cor_upload = "red";
                                                }

                                                //
                                                // STATUS REMOTAS
                                                //

                                                string cor_remota = "gray";
                                                string status_remota_texto = "Sem remotas";
                                                int status_remota = 0;

                                                if (gateway.RemotasList != null)
                                                {
                                                    // comunicando
                                                    if (gateway.RemotasList.Count > 0)
                                                    {
                                                        status_remota = 1;
                                                        status_remota_texto = "";
                                                    }

                                                    // percorre remotas
                                                    foreach (GatewayRemotasDominio remota in gateway.RemotasList)
                                                    {
                                                        // verifica status
                                                        if (remota.Status == 0)
                                                        {
                                                            status_remota = 2;
                                                            status_remota_texto += string.Format("Remota {1} não comunicando - [{0}]", remota.DataStatusTexto, remota.Remota);
                                                        }

                                                        if (remota.Status == 1)
                                                        {
                                                            status_remota_texto += string.Format("Remota {1} Normal - [{0}]", remota.DataStatusTexto, remota.Remota);
                                                        }

                                                        if (remota.Status == -1)
                                                        {
                                                            status_remota = 2;
                                                            status_remota_texto += string.Format("Remota {1} Status indefinido - [{0}]", remota.DataStatusTexto, remota.Remota);
                                                        }

                                                        // pula linha
                                                        status_remota_texto += "<br>";
                                                    }
                                                }

                                                switch (status_remota)
                                                {
                                                    case 0:
                                                    default:
                                                        Status_Sort += "B";

                                                        status_remota_texto = "Sem remotas";
                                                        cor_remota = "orange";
                                                        break;

                                                    case 1:
                                                        Status_Sort += "Z";

                                                        cor_remota = "green";
                                                        break;

                                                    case 2:
                                                        Status_Sort += "A";

                                                        cor_remota = "red";
                                                        break;
                                                }

                                                //
                                                // STATUS FALHA ENVIO ARQUIVOS
                                                //

                                                if (gateway.NumFalhas > 0 && (gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado && gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente && gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.SCDE))
                                                {
                                                    Status_Sort += "A";
                                                }
                                                else
                                                {
                                                    Status_Sort += "Z";
                                                }

                                                //
                                                // STATUS GATEWAY
                                                //

                                                if (gateway.IDTipoTempo < 10)
                                                {
                                                    Status_Sort += "A";
                                                }
                                                else
                                                {
                                                    Status_Sort += "Z";
                                                }

                                            }

                                            <td>
                                                <span style="display:none;">@Status_Sort</span>

                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@gateway.StatusTexto"><i class="fa fa-upload" style="font-size:20px; color:@cor_upload"></i></span>&nbsp;&nbsp;
                                                <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@status_remota_texto"><i class="fa fa-sitemap" style="font-size:20px; color:@cor_remota"></i></span>&nbsp;&nbsp;

                                                @{
                                                    if (IDTipoAcesso == TIPO_ACESSO.GESTAL_ADMIN || IDTipoAcesso == TIPO_ACESSO.GESTAL_SUPORTE)
                                                    {
                                                        if (gateway.NumFalhas > 0 && (gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado && gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente && gateway.IDTipoTempo != TIPO_TEMPO_GATEWAY.SCDE))
                                                        {
                                                            string datahora_str = String.Format("{0:G}", gateway.DataHora);
                                                            string status_FalhaEnvio_texto = string.Format("Ocorreram {0} Falha(s) de Envio nos 3 dias anteriores a última atualização", gateway.NumFalhas);

                                                            <a href='@("/Relatorios/Relat_Eventos_Gateway?IDCliente=" + @gateway.IDCliente.ToString() + "&IDGateway=" + @gateway.IDGateway.ToString() + "&DataHora=" + datahora_str)'><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@status_FalhaEnvio_texto"><i class="fa fa-warning" style="font-size:20px; color:red"></i>&nbsp;&nbsp;</span></a>
                                                        }
                                                    }
                                                }

                                                @{
                                                    if (gateway.IDTipoTempo < 10)
                                                    {
                                                        string status_Gateway_texto = "Gateway Bloqueada";

                                                        switch ((int)gateway.IDTipoTempo)
                                                        {
                                                            case TIPO_TEMPO_GATEWAY.GatewayBloqueada:
                                                                status_Gateway_texto = "Gateway Bloqueada";
                                                                break;

                                                            case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:
                                                                status_Gateway_texto = "Ignorar Falha de Upload (Start Up não realizado)";
                                                                break;

                                                            case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:
                                                                status_Gateway_texto = "Ignorar Falha de Upload (Pendência do Cliente)";
                                                                break;

                                                            case TIPO_TEMPO_GATEWAY.SCDE:
                                                                status_Gateway_texto = "Ignorar Falha de Upload (SCDE)";
                                                                break;
                                                        }

                                                        <span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@status_Gateway_texto"><i class="fa fa-ban" style="font-size:20px; color:red"></i>&nbsp;&nbsp;</span>
                                                    }
                                                }
                                            </td>
                                        </tr>
                                    }
                                }
                            }

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")


    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

    $(document).ready(function () {

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "portugues-pre": function (data) {
                var a = 'a';
                var e = 'e';
                var i = 'i';
                var o = 'o';
                var u = 'u';
                var c = 'c';
                var special_letters = {
                    "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                    "É": e, "é": e, "Ê": e, "ê": e,
                    "Í": i, "í": i, "Î": i, "î": i,
                    "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                    "Ú": u, "ú": u, "Ü": u, "ü": u,
                    "ç": c, "Ç": c
                };
                for (var val in special_letters)
                    data = data.split(val).join(special_letters[val]).toLowerCase();
                return data;
            },
            "portugues-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },
            "portugues-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "numero-pre": function (a) {

                var x = a;

                if (a == "-") {
                    x = "-1";
                }
                else {
                    x = a.replace(".", "");
                    x = x.replace(",", ".");
                }

                return parseFloat(x);
            },

            "numero-asc": function (a, b) {
                return ((a < b) ? -1 : ((a > b) ? 1 : 0));
            },

            "numero-desc": function (a, b) {
                return ((a < b) ? 1 : ((a > b) ? -1 : 0));
            }
        });

        $('.dataTables-gateways').DataTable({
            "iDisplayLength": 18,
            dom: 'ftp',
            "bAutoWidth": false,
            "aoColumnDefs": [
                        { "aTargets": [0], "sType": "portugues" },
                        { "aTargets": [1], "sType": "portugues" },
                        { "aTargets": [2], "sType": "portugues" },
                        { "aTargets": [3], "sType": "portugues" },
                        { "aTargets": [4], "sType": "portugues" },
                        { "aTargets": [5], "sType": "portugues" },
                        { "aTargets": [6], "sType": "portugues" },
            ],
            "aoColumns": [
            { sWidth: "6%" },
            { sWidth: "22%" },
            { sWidth: "11%" },
            { sWidth: "13%" },
            { sWidth: "16%" },
            { sWidth: "19%" },
            { sWidth: "13%" },
            ],

            "language": {
                "paginate": {
                    "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                },
                "search": "@SmartEnergy.Resources.ComumTexts.search",
                "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                "info": "@SmartEnergy.Resources.ComumTexts.info",
                "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
            }

            });
        });

        $(".bottom").tooltip({
            placement: "bottom"
        });

    </script>
}


