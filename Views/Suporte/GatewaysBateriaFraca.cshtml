﻿@model IEnumerable<SmartEnergyLib.SQL.SupervGatewaysBateriaFracaDominio>

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoGatewaysBateriaFraca;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #333333 !important;
    }

    .icones-danger {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #ED5565 !important;
    }

    .icones-warning {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #f8ac59 !important;
    }

    .icones-primary {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #1ab394 !important;
    }

    .tipo1-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo2-bg {
        background-color: #C0C0C0 !important;
        color: #000000 !important;
    }

    .tipo3-bg {
        background-color: #A9A9A9 !important;
        color: #ffffff !important;
    }

    .tipo4-bg {
        background-color: #696969 !important;
        color: #ffffff !important;
    }

    .tipo5-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo6-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .dataTables-gateways tr.even:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    .dataTables-gateways tr.odd:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    .tooltip-inner {
        position: relative;
        display: inline-block;
        max-width: 350px;
        /* If max-width does not work, try using width instead */
        white-space: pre-wrap;
        text-align: left;
    }
</style>



<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoGatewaysBateriaFraca</h4>
                </div>
                <div class="panel-body">

                    <div class="row">
                        <div class="col-lg-12">
                            <table id="example" class="table table-bordered table-hover dataTables-gateways">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.ClientesNomeFantasia</th>
                                        <th>Gestor</th>
                                        <th>ID</th>
                                        <th>Gateway</th>
                                        <th>Modelo (Versão)</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.Equipamento</th>
                                        <th>Resets</th>
                                        <th>Dias em Bateria</th>
                                        <th>Início<br />Bateria Normal</th>
                                        <th>Início<br />Bateria Fraca</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>

                                    @{
                                        foreach (SupervGatewaysBateriaFracaDominio gateway in Model)
                                        {
                                            // copia data e hora
                                            string DataHora = String.Format("{0:G}", gateway.DataHora);
                                            string DataHora_Sort = String.Format("{0:yyyyMMddHHmmss}", gateway.DataHora);

                                            // copia data e hora equipamento
                                            string DataEq = String.Format("{0:G}", gateway.DataEq);
                                            string DataEq_Sort = String.Format("{0:yyyyMMddHHmmss}", gateway.DataEq);

                                            // copia inicio bateria ok
                                            string DataBateriaOK = String.Format("{0:d}", gateway.InicioBateriaOK);
                                            string DataBateriaOK_Sort = String.Format("{0:yyyyMMdd}", gateway.InicioBateriaOK);

                                            // copia inicio bateria fraca
                                            string DataBateriaFraca = String.Format("{0:d}", gateway.InicioBateriaFraca);
                                            string DataBateriaFraca_Sort = String.Format("{0:yyyyMMdd}", gateway.InicioBateriaFraca);
                                                                                        

                                            <tr>
                                                <td>@gateway.IDCliente</td>
                                                <td>@gateway.Fantasia.ToUpper()</td>
                                                <td>@gateway.NomeConsultor</td>
                                                <td>@gateway.IDGateway</td>
                                                <td>@gateway.Nome</td>
                                                <td>@gateway.ModeloEq<br />(@gateway.VersaoEq)</td>
                                                <td><span style="display:none;">@DataHora_Sort    </span>@DataHora</td>
                                                <td><span style="display:none;">@DataEq_Sort    </span>@DataEq</td>
                                                <td>@gateway.NumResets</td>
                                                <td>@gateway.DiasEmBateria</td>
                                                <td><span style="display:none;">@DataBateriaOK_Sort    </span>@DataBateriaOK</td>
                                                <td><span style="display:none;">@DataBateriaFraca_Sort    </span>@DataBateriaFraca</td>

                                                <td>
                                                    <a onclick="ObservacoesGateway(event, @gateway.IDGateway)">
                                                        @{

                                                            // leio ultima observacao da gateway
                                                            ObservacaoGatewayMetodos observacaoMetodos = new ObservacaoGatewayMetodos();
                                                            ObservacaoGatewayDominio ultimaObservacao = observacaoMetodos.ListarPrimeiroGateway(gateway.IDGateway);

                                                            // le tags
                                                            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
                                                            List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

                                                            // a principio sem observacao
                                                            string descricao = "Sem observação";

                                                            if (ultimaObservacao != null)
                                                            {
                                                                foreach (ObservacaoTagsDominio tag in tags)
                                                                {
                                                                    if (ultimaObservacao.IDTag == tag.IDTag)
                                                                    {
                                                                        descricao = String.Format("{0:d}", @ultimaObservacao.DataHora) + "\n \n" + @tag.Descricao + "\n \n" + @ultimaObservacao.Observacao;
                                                                    }
                                                                }

                                                                if (ultimaObservacao.IDTag > 0 && ultimaObservacao.IDTag < 100)
                                                                {
                                                                    <i class="fa fa-comment icones-danger" data-toggle="tooltip" title="@descricao"></i>
                                                                }

                                                                if (ultimaObservacao.IDTag > 100 && ultimaObservacao.IDTag < 200)
                                                                {
                                                                    <i class="fa fa-comment icones-warning" data-toggle="tooltip" title="@descricao"></i>
                                                                }

                                                                if (ultimaObservacao.IDTag > 200 && ultimaObservacao.IDTag < 300)
                                                                {
                                                                    <i class="fa fa-comment icones-primary" data-toggle="tooltip" title="@descricao"></i>
                                                                }

                                                                if (ultimaObservacao.IDTag == 0)
                                                                {
                                                                    <i class="fa fa-comment icones" data-toggle="tooltip" title="@descricao"></i>
                                                                }
                                                            }

                                                            else
                                                            {
                                                                <i class="fa fa-comment icones" data-toggle="tooltip" title="@descricao"></i>
                                                            }
                                                        }
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>

                            @Html.Hidden("IDGatewaySelecionado", 0)
                            @Html.Hidden("IDObservacaoSelecionado", 0)

                            <div class="modal inmodal animated fadeIn" id="ModalObservacoes" tabindex="-1" role="dialog" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-editar-header">
                                            <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacoes</span>


                                            <div class="pull-right dashboard-tools link_branco" style="margin-top:-2px;">
                                                <h4>
                                                    <a href="#" onclick="javascript:ObservacaoGateway(event, 0, 0);" id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                                                </h4>
                                            </div>

                                        </div>
                                        <div class="modal-body">

                                            <div id="Observacoes_resultado" min-height:500px;">
                                                @Html.Partial("_ObservacoesGateway")
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" onclick="FecharObservacoesGateway();">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="modal inmodal animated fadeIn" id="ModalObservacao" tabindex="-1" role="dialog" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-editar-header">
                                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                                            <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacao</span>
                                        </div>
                                        <div class="modal-body">

                                            <div id="Observacao_resultado" min-height:500px;">
                                                @Html.Partial("_ObservacaoGateway")
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                            <button type="button" class="btn btn-primary" onclick="SalvarObservacaoGateway();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")

    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


    <script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            $('.dataTables-gateways').DataTable({
                "iDisplayLength": 10,
                dom: 'Bftp',
                buttons: [
                    {
                        extend: 'excelHtml5',
                        filename: 'GatewaysBateriaFraca',
                        title: 'Gateways com Bateria Fraca',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                    {
                        extend: 'pdfHtml5',
                        filename: 'GatewaysBateriaFraca',
                        title: 'Gateways com Bateria Fraca',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                ],

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "sType": "numero" },
                            { "aTargets": [1], "sType": "portugues" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "numero" },
                            { "aTargets": [4], "sType": "portugues" },
                            { "aTargets": [5], "sType": "portugues" },
                            { "aTargets": [6], "sType": "portugues" },
                            { "aTargets": [7], "sType": "portugues" },
                            { "aTargets": [8], "sType": "numero" },
                            { "aTargets": [9], "sType": "numero" },
                            { "aTargets": [10], "sType": "portugues" },
                            { "aTargets": [11], "sType": "portugues" },
                            { "aTargets": [12], "bSortable": false },
                ],
                "aoColumns": [
                { sWidth: "3%" },
                { sWidth: "11%" },
                { sWidth: "10%" },
                { sWidth: "3%" },
                { sWidth: "11%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "6%" },
                { sWidth: "6%" },
                { sWidth: "10%" },
                { sWidth: "10%" },
                { sWidth: "5%" },
                ],
                'order': [10, 'asc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            $(".bottom").tooltip({
                placement: "bottom"
            });

        });


        function ObservacoesGateway(e, IDGateway) {

            e.stopPropagation();

            // apresenta modal
            $("#ModalObservacoes").modal("show");

            // aguarde
            $('#Observacoes_resultado').css("display", "none");

            // IDGateway
            document.getElementById("IDGatewaySelecionado").value = IDGateway;

            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/_ObservacoesGateway',
                dataType: 'html',
                data: { "IDGateway": IDGateway },
                cache: false,
                async: true,
                success: function (data) {

                    $('#Observacoes_resultado').css("display", "block");
                    $('#Observacoes_resultado').html(data);
                }
            });
        }

        function FecharObservacoesGateway() {

            // fecha modal
            $("#ModalObservacoes").modal("hide");

            // atualiza pagina
            location.reload();
        }

        function ObservacaoGateway(e, IDGateway, IDObservacao) {

            e.stopPropagation();

            // apresenta modal
            $("#ModalObservacao").modal("show");

            // aguarde
            $('#Observacao_resultado').css("display", "none");

            // IDGateway e IDObservacao
            if (IDGateway == 0) {
                // IDGateway
                IDGateway = document.getElementById("IDGatewaySelecionado").value;
            }
            else {
                document.getElementById("IDGatewaySelecionado").value = IDGateway;
            }

            document.getElementById("IDObservacaoSelecionado").value = IDObservacao;

            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/_ObservacaoGateway',
                dataType: 'html',
                data: { "IDGateway": IDGateway, "IDObservacao": IDObservacao },
                cache: false,
                async: true,
                success: function (data) {

                    $('#Observacao_resultado').css("display", "block");
                    $('#Observacao_resultado').html(data);
                }
            });
        }


        function SalvarObservacaoGateway() {

            // IDObservacao
            var IDObservacao = document.getElementById("IDObservacaoSelecionado").value;

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // IDTag
            var IDTag = $("#tags").val();

            // data
            var data_relat = document.querySelector('[name="data_relat"]').value;

            // cliente visualiza
            var ClienteVisualizaObj = document.getElementsByName('ClienteVisualiza');
            var ClienteVisualiza = ClienteVisualizaObj[0].checked;

            // Observacao
            var ObservacaoTexto = document.getElementById("ObservacaoTexto").value;

            // salvar
            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/ObservacaoGateway_Salvar',
                data: { 'IDObservacao': IDObservacao, 'IDGateway': IDGateway, 'IDTag': IDTag, 'ClienteVisualiza': ClienteVisualiza, 'ObservacaoData': data_relat, 'ObservacaoTexto': ObservacaoTexto },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Suporte/_ObservacoesGateway',
                            dataType: 'html',
                            data: { "IDGateway": IDGateway },
                            cache: false,
                            async: true,
                            success: function (data) {

                                $('#Observacoes_resultado').css("display", "block");
                                $('#Observacoes_resultado').html(data);
                            }
                        });

                    }, 100);
                },
                error: function (response) {

                }
            });
        }

        function ResolvidoObservacaoGateway(IDObservacao) {
            event.stopPropagation();

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // resolvido
            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/ObservacaoGateway_Resolvido',
                data: { 'IDObservacao': IDObservacao },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Suporte/_ObservacoesGateway',
                            dataType: 'html',
                            data: { "IDGateway": IDGateway },
                            cache: false,
                            async: true,
                            success: function (data) {

                                $('#Observacoes_resultado').css("display", "block");
                                $('#Observacoes_resultado').html(data);
                            }
                        });

                    }, 100);
                },
                error: function (response) {

                }
            });
        };

        function ExcluirObservacaoGateway(IDObservacao, DataTexto) {
            event.stopPropagation();

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // titulo
            titulo = "Deseja excluir a Observação?<br/>" + DataTexto;

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/Suporte/ObservacaoGateway_Excluir',
                    data: { 'IDObservacao': IDObservacao },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            $.ajax(
                            {
                                type: 'GET',
                                url: '/Suporte/_ObservacoesGateway',
                                dataType: 'html',
                                data: { "IDGateway": IDGateway },
                                cache: false,
                                async: true,
                                success: function (data) {

                                    $('#Observacoes_resultado').css("display", "block");
                                    $('#Observacoes_resultado').html(data);
                                }
                            });

                        }, 100);
                    },
                    error: function (response) {

                    }
                });
            });
        };

    </script>
}

