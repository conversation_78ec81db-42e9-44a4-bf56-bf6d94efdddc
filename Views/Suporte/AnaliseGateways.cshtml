﻿@model IEnumerable<SmartEnergyLib.SQL.SupervGatewaysDominio>

@using System.Globalization
@using SmartEnergyLib.Declaracoes
@using SmartEnergyLib.SQL

@{
    ViewBag.Title = @SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoGatewaysAtraso;
}

<style>
    .icones {
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #333333 !important;
    }

    .icones-danger {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #ED5565 !important;
    }

    .icones-warning {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #f8ac59 !important;
    }

    .icones-primary {
        text-shadow: 0 0 5px #000000;
        padding: 0px 5px;
        font-size: 18px;
        float: left;
        color: #1ab394 !important;
    }

    .tipo1-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo2-bg {
        background-color: #C0C0C0 !important;
        color: #000000 !important;
    }

    .tipo3-bg {
        background-color: #A9A9A9 !important;
        color: #ffffff !important;
    }

    .tipo4-bg {
        background-color: #696969 !important;
        color: #ffffff !important;
    }

    .tipo5-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .tipo6-bg {
        background-color: #DCDCDC !important;
        color: #000000 !important;
    }

    .dataTables-gateways tr.even:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    .dataTables-gateways tr.odd:hover td {
        background-color: #E0E0E0 !important;
        color: #000000 !important;
    }

    .tooltip-inner {
        position: relative;
        display: inline-block;
        max-width: 350px;
        /* If max-width does not work, try using width instead */
        white-space: pre-wrap;
        text-align: left;
    }
</style>



<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-lg-12">
            <div class="panel panel-title">
                <div class="panel-heading">
                    <h4>@SmartEnergy.Resources.MenuTexts.MenuLateralSupervisaoGatewaysAtraso</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-6">

                            @{  
                                var GatewaysAtraso = ViewBag.GatewaysAtraso;
                                var GatewaysAtraso_Classe = "navy-bg";

                                if (GatewaysAtraso >= 200)
                                {
                                    GatewaysAtraso_Classe = "red-bg";
                                }
                            }

                            <div class="widget style1 @GatewaysAtraso_Classe">
                                <div class="row">

                                    @{
                                        if (GatewaysAtraso >= 200)
                                        {
                                            <blink>
                                                <div class="col-xs-4">
                                                    <i class="fa fa-clock-o fa-5x"></i>
                                                </div>
                                                <div class="col-xs-8 text-right">
                                                    <span> Gateways em Atraso </span>
                                                    <h2 class="font-bold">@GatewaysAtraso</h2>
                                                </div>
                                            </blink>
                                        }
                                        else
                                        {   
                                            <div class="col-xs-4">
                                                <i class="fa fa-clock-o fa-5x"></i>
                                            </div>
                                            <div class="col-xs-8 text-right">
                                                <span>Gateways em Atraso </span>
                                                <h2 class="font-bold">@GatewaysAtraso</h2>
                                            </div>
                                        }
                                    }

                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <table class="table">
                                <tbody>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo1-bg m-r-sm">@ViewBag.GatewaysAtraso_3dias</button>
                                            Atraso menor que 3 dias
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo2-bg m-r-sm">@ViewBag.GatewaysAtraso_3_7dias</button>
                                            Atraso entre 3 e 7 dias
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo3-bg m-r-sm">@ViewBag.GatewaysAtraso_7_23dias</button>
                                            Atraso entre 7 e 23 dias
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo4-bg m-r-sm">@ViewBag.GatewaysAtraso_23dias</button>
                                            Atraso acima de 23 dias
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo5-bg m-r-sm">@ViewBag.GatewaysRelogioErrado</button>
                                            Relógio da Gateway com diferença
                                        </td>
                                        <td style="width: 50%;">
                                            <button type="button" class="btn tipo6-bg m-r-sm">@ViewBag.GatewaysFaltaArquivo</button>
                                            Faltam arquivos
                                        </td>
                                    </tr>
                                    <tr>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-12">
                            <select class="select2_tipo form-control" id="Tipos" onchange="SelecionouTipo()">
                                <option value='0'>Todas as Gateways em atraso</option>
                                <option value='1'>Atraso menor que 3 dias</option>
                                <option value='2'>Atraso entre 3 e 7 dias</option>
                                <option value='3'>Atraso entre 7 e 23 dias</option>
                                <option value='4'>Atraso maior que 23 dias</option>
                                <option value='5'>Gateways com diferença entre o relógio da gateway e do servidor maior que 2 horas</option>
                                <option value='6'>Gateways sem receber todos os arquivos</option>
                            </select>
                            <br />
                            <table id="example" class="table table-bordered table-hover dataTables-gateways">
                                <thead>
                                    <tr>
                                        <th>Tipo Atraso</th>
                                        <th>ID</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.ClientesNomeFantasia</th>
                                        <th>Gestor</th>
                                        <th>ID</th>
                                        <th>Gateway</th>
                                        <th>Modelo (Versão)</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.Equipamento</th>
                                        <th>Envio</th>
                                        <th>IP | @SmartEnergy.Resources.SupervisaoTexts.Sinal</th>
                                        <th>ICC</th>
                                        <th><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@SmartEnergy.Resources.SupervisaoTexts.Total @SmartEnergy.Resources.SupervisaoTexts.Falhas - @SmartEnergy.Resources.SupervisaoTexts.Ultimos 3 @SmartEnergy.Resources.SupervisaoTexts.Meses">@SmartEnergy.Resources.SupervisaoTexts.Falhas</span></th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tfoot>
                                    <tr>
                                        <th>Tipo Atraso</th>
                                        <th>ID</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.ClientesNomeFantasia</th>
                                        <th>Gestor</th>
                                        <th>ID</th>
                                        <th>Gateway</th>
                                        <th>Modelo (Versão)</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.UltimaAtualizacao</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.Equipamento</th>
                                        <th>Envio</th>
                                        <th>IP | @SmartEnergy.Resources.SupervisaoTexts.Sinal</th>
                                        <th>ICC</th>
                                        <th>@SmartEnergy.Resources.SupervisaoTexts.Falhas</th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                                <tbody>

                                    @{
                                        foreach (SupervGatewaysDominio gateway in Model)
                                        {

                                            // listo historico de falhas
                                            AnaliseGatewaysHistMetodos analiseGatewaysMetodos = new AnaliseGatewaysHistMetodos();
                                            List<AnaliseGatewaysHistDominio> analiseGatewaysHist = analiseGatewaysMetodos.ListarPorIDGateway(gateway.IDGateway);

                                            // total de falhas dos ultimos 3 meses
                                            TimeSpan tresMeses = new TimeSpan(90, 0, 0, 0);
                                            int falhas_Total = analiseGatewaysHist.Count(x => (DateTime.Now - x.DataFim <= tresMeses));

                                            string Classe = "tipo" + gateway.TipoAtraso + "-bg";

                                            // copia data e hora
                                            string DataHora = String.Format("{0:G}", gateway.DataHora);

                                            // copia data e hora equipamento
                                            string DataEq = String.Format("{0:G}", gateway.DataEq);

                                            // dados do celular
                                            string infos_celular = "";

                                            if (gateway.OP != null)
                                            {
                                                infos_celular = "ICC [" + ((gateway.ICC.Length == 0) ? "---" : gateway.ICC) + "]<br>";
                                                infos_celular += "IMEI [" + ((gateway.IMEI.Length == 0) ? "---" : gateway.IMEI) + "]<br>";
                                                infos_celular += "FAB [" + ((gateway.FAB.Length == 0) ? "---" : gateway.FAB) + "]<br>";
                                                infos_celular += "MOD [" + ((gateway.MOD.Length == 0) ? "---" : gateway.MOD) + "]<br>";
                                                infos_celular += "OP [" + ((gateway.OP.Length == 0) ? "---" : gateway.OP) + "]<br>";
                                                infos_celular += "MCC [" + ((gateway.MCC.Length == 0) ? "---" : gateway.MCC) + "]<br>";
                                                infos_celular += "MNC [" + ((gateway.MNC.Length == 0) ? "---" : gateway.MNC) + "]<br>";
                                                infos_celular += "LAC [" + ((gateway.LAC.Length == 0) ? "---" : gateway.LAC) + "]<br>";
                                                infos_celular += "CID [" + ((gateway.CID.Length == 0) ? "---" : gateway.CID) + "]<br>";
                                            }

                                            if (infos_celular.Length == 0)
                                            {
                                                infos_celular = "Sem informações";
                                            }

                                            // tempo de atualizacao
                                            string tempo_atualizacao = "Indefinido";

                                            switch (gateway.IDTipoTempo)
                                            {
                                                case TIPO_TEMPO_GATEWAY.GatewayBloqueada:                   // 0 - gateway bloqueada
                                                    tempo_atualizacao = "Bloqueada";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Ignorar_StartUpNaoRealizado:        // 1 - ignorar - start up nao realizado
                                                    tempo_atualizacao = "StartUp";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Ignorar_PendenciaCliente:           // 2 - ignorar - pendencia do cliente
                                                    tempo_atualizacao = "Pendência";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.SCDE:                               // 3 - ignorar - SCDE
                                                    tempo_atualizacao = "SCDE";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_15minutos:                    // 12 - a cada 15 minutos
                                                    tempo_atualizacao = "A cada 15 min";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_hora:                         // 13 - a cada hora
                                                    tempo_atualizacao = "A cada hora";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_diariamente:                  // 14 - diariamente
                                                    tempo_atualizacao = "Diariamente";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_semanalmente:                 // 15 - semanalmente
                                                    tempo_atualizacao = "Semanalmente";
                                                    break;

                                                case TIPO_TEMPO_GATEWAY.Envio_mensalmente:                  // 16 - mensalmente
                                                    tempo_atualizacao = "Mensalmente";
                                                    break;
                                            }

                                            <tr class="@Classe" style="cursor:pointer;" onclick="location.href='@("/Supervisao/Gateway?IDGateway=" + @gateway.IDGateway.ToString())'">
                                                <td>@gateway.TipoAtraso</td>
                                                <td>@gateway.IDCliente</td>
                                                <td>@gateway.Fantasia.ToUpper()</td>
                                                <td>@gateway.NomeConsultor</td>
                                                <td>@gateway.IDGateway</td>
                                                <td>@gateway.Nome</td>
                                                <td>@gateway.ModeloEq&nbsp;<br />(@gateway.VersaoEq)&nbsp;</td>
                                                <td>@DataHora</td>
                                                <td>@DataEq</td>
                                                <td>@tempo_atualizacao</td>
                                                <td><span class="bottom" data-toggle="tooltip" data-html="true" data-placement="bottom" title="@infos_celular">@gateway.StatusEq.ToUpper()</span></td>
                                                <td>_@((gateway.ICC.Length == 0) ? "" : gateway.ICC)</td>
                                                <td>@falhas_Total</td>
                                                <td>
                                                    <a href='@("/Suporte/AnaliseGatewaysHist?IdGateway=" + @gateway.IDGateway.ToString())'>
                                                        <i class="fa fa-list icones" data-toggle="tooltip" title="@SmartEnergy.Resources.MetasTexts.Historico"></i>
                                                    </a>
                                                    <a onclick="ObservacoesGateway(event, @gateway.IDGateway)">
                                                        @{

                                                            // leio ultima observacao da gateway
                                                            ObservacaoGatewayMetodos observacaoMetodos = new ObservacaoGatewayMetodos();
                                                            ObservacaoGatewayDominio ultimaObservacao = observacaoMetodos.ListarPrimeiroGateway(gateway.IDGateway);

                                                            // le tags
                                                            ListaTiposMetodos listaMetodos = new ListaTiposMetodos();
                                                            List<ObservacaoTagsDominio> tags = listaMetodos.ListarTodos_ObservacaoTags();

                                                            // a principio sem observacao
                                                            string descricao = "Sem observação";

                                                            if (ultimaObservacao != null)
                                                            {
                                                                foreach (ObservacaoTagsDominio tag in tags)
                                                                {
                                                                    if (ultimaObservacao.IDTag == tag.IDTag)
                                                                    {
                                                                        descricao = String.Format("{0:d}", @ultimaObservacao.DataHora) + "\n \n" + @tag.Descricao + "\n \n" + @ultimaObservacao.Observacao;
                                                                    }
                                                                }

                                                                if (ultimaObservacao.IDTag > 0 && ultimaObservacao.IDTag < 100)
                                                                {
                                                                    <i class="fa fa-comment icones-danger" data-toggle="tooltip" title="@descricao"></i>
                                                                }

                                                                if (ultimaObservacao.IDTag > 100 && ultimaObservacao.IDTag < 200)
                                                                {
                                                                    <i class="fa fa-comment icones-warning" data-toggle="tooltip" title="@descricao"></i>
                                                                }

                                                                if (ultimaObservacao.IDTag > 200 && ultimaObservacao.IDTag < 300)
                                                                {
                                                                    <i class="fa fa-comment icones-primary" data-toggle="tooltip" title="@descricao"></i>
                                                                }

                                                                if (ultimaObservacao.IDTag == 0)
                                                                {
                                                                    <i class="fa fa-comment icones" data-toggle="tooltip" title="@descricao"></i>
                                                                }
                                                            }

                                                            else
                                                            {
                                                                <i class="fa fa-comment icones" data-toggle="tooltip" title="@descricao"></i>
                                                            }
                                                        }
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    }

                                </tbody>
                            </table>
                            
                            @Html.Hidden("IDGatewaySelecionado", 0)
                            @Html.Hidden("IDObservacaoSelecionado", 0)

                            <div class="modal inmodal animated fadeIn" id="ModalObservacoes" tabindex="-1" role="dialog" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-editar-header">
                                            <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacoes</span>


                                            <div class="pull-right dashboard-tools link_branco" style="margin-top:-2px;">
                                                <h4>
                                                    <a href="#" onclick="javascript:ObservacaoGateway(event, 0, 0);" id="BotaoAdicionar" title="@SmartEnergy.Resources.ComumTexts.BotaoInserir"><i class="fa fa-plus"></i></a>
                                                </h4>
                                            </div>

                                        </div>
                                        <div class="modal-body">

                                            <div id="Observacoes_resultado" min-height:500px;">
                                                @Html.Partial("_ObservacoesGateway")
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" onclick="FecharObservacoesGateway();">@SmartEnergy.Resources.ComumTexts.BotaoFechar</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="modal inmodal animated fadeIn" id="ModalObservacao" tabindex="-1" role="dialog" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-editar-header">
                                            <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only modal-tools-header">Close</span></button>
                                            <i class="fa fa-check-square-o modal-editar-title"></i><span class="modal-editar-title">&nbsp;&nbsp;@SmartEnergy.Resources.SupervisaoTexts.Observacao</span>
                                        </div>
                                        <div class="modal-body">

                                            <div id="Observacao_resultado" min-height:500px;">
                                                @Html.Partial("_ObservacaoGateway")
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-default" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoCancelar</button>
                                            <button type="button" class="btn btn-primary" onclick="SalvarObservacaoGateway();" data-dismiss="modal">@SmartEnergy.Resources.ComumTexts.BotaoSalvar</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    @Styles.Render("~/plugins/sweetAlertStyles")
    @Styles.Render("~/datetimePickerStyles")
    @Styles.Render("~/Content/plugins/iCheck/iCheckStyles")
}

@section Scripts {
    @Scripts.Render("~/plugins/jqueryui")
    @Scripts.Render("~/plugins/sweetAlert")
    @Scripts.Render("~/datetimePicker")
    @Scripts.Render("~/plugins/iCheck")

    @if (@CultureInfo.CurrentCulture.Name == "en")
    {
    }

    @if (@CultureInfo.CurrentCulture.Name == "es")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_es.js"></script>
    }

    @if (@CultureInfo.CurrentCulture.Name == "pt-BR")
    {
        <script type="text/javascript" language="javascript" src="~/Scripts/plugins/validate/messages_pt-br.js"></script>
    }


<script type="text/javascript">

        $(document).ready(function () {

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "portugues-pre": function (data) {
                    var a = 'a';
                    var e = 'e';
                    var i = 'i';
                    var o = 'o';
                    var u = 'u';
                    var c = 'c';
                    var special_letters = {
                        "Á": a, "á": a, "Ã": a, "ã": a, "À": a, "à": a,
                        "É": e, "é": e, "Ê": e, "ê": e,
                        "Í": i, "í": i, "Î": i, "î": i,
                        "Ó": o, "ó": o, "Õ": o, "õ": o, "Ô": o, "ô": o,
                        "Ú": u, "ú": u, "Ü": u, "ü": u,
                        "ç": c, "Ç": c
                    };
                    for (var val in special_letters)
                        data = data.split(val).join(special_letters[val]).toLowerCase();
                    return data;
                },
                "portugues-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },
                "portugues-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "numero-pre": function (a) {
                    var x = (a == "-") ? 0 : a.replace(/,/, ".");
                    return parseFloat(x);
                },

                "numero-asc": function (a, b) {
                    return ((a < b) ? -1 : ((a > b) ? 1 : 0));
                },

                "numero-desc": function (a, b) {
                    return ((a < b) ? 1 : ((a > b) ? -1 : 0));
                }
            });

            jQuery.extend(jQuery.fn.dataTableExt.oSort, {
                "datetime-br-pre": function (a) {
                    if (!a) return 0; // Se a célula estiver vazia, retorna 0
                    let parts = a.split(' '); // Divide a string entre data e hora
                    let dateParts = parts[0].split('/'); // Divide a data dd/mm/aaaa
                    let timeParts = parts[1].split(':'); // Divide a hora hh:mm

                    return new Date(
                        parseInt(dateParts[2], 10),     // Ano
                        parseInt(dateParts[1], 10) - 1, // Mês (base 0)
                        parseInt(dateParts[0], 10),     // Dia
                        parseInt(timeParts[0], 10),     // Hora
                        parseInt(timeParts[1], 10)      // Minuto
                    ).getTime();
                },

                "datetime-br-asc": function (a, b) {
                    return a - b;
                },

                "datetime-br-desc": function (a, b) {
                    return b - a;
                }
            });

            $('.dataTables-gateways').DataTable({
                "iDisplayLength": 10,
                dom: 'Bftp',
                buttons: [
                    {
                        extend: 'excelHtml5',
                        filename: 'GatewaysAtraso',
                        title: 'Gateways com Atraso no Envio de Arquivos',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-excel-o" data-toggle="tooltip" data-placement="left" title="Excel"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                    {
                        extend: 'pdfHtml5',
                        filename: 'GatewaysAtraso',
                        title: 'Gateways com Atraso no Envio de Arquivos',
                        text: '<span style="font-size: 18px; margin: 5px 5px 5px 5px;"><i class="fa fa-file-pdf-o" data-toggle="tooltip" data-placement="left" title="PDF"></span>',
                        className: 'btn btn-default btn-xs'
                    },
                ],

                "bAutoWidth": false,
                "aoColumnDefs": [
                            { "aTargets": [0], "bVisible": false },
                            { "aTargets": [1], "sType": "numero" },
                            { "aTargets": [2], "sType": "portugues" },
                            { "aTargets": [3], "sType": "portugues" },
                            { "aTargets": [4], "sType": "numero" },
                            { "aTargets": [5], "sType": "portugues" },
                            { "aTargets": [6], "sType": "portugues" },
                            { "aTargets": [7], "sType": "datetime-br" },
                            { "aTargets": [8], "sType": "datetime-br" },
                            { "aTargets": [9], "sType": "portugues" },
                            { "aTargets": [10], "sType": "portugues" },
                            { "aTargets": [11], "sType": "portugues" },
                            { "aTargets": [12], "sType": "numero" },
                            { "aTargets": [13], "bSortable": false },
                ],
                "aoColumns": [
                { sWidth: "0%" },
                { sWidth: "3%" },
                { sWidth: "15%" },
                { sWidth: "8%" },
                { sWidth: "3%" },
                { sWidth: "13%" },
                { sWidth: "8%" },
                { sWidth: "8%" },
                { sWidth: "8%" },
                { sWidth: "8%" },
                { sWidth: "8%" },
                { sWidth: "10%" },
                { sWidth: "4%" },
                { sWidth: "5%" },
                ],
                'order': [7, 'desc'],

                "language": {
                    "paginate": {
                        "previous": "@SmartEnergy.Resources.ComumTexts.previous",
                        "next": "@SmartEnergy.Resources.ComumTexts.next"
                    },
                    "search": "@SmartEnergy.Resources.ComumTexts.search",
                    "lengthMenu": "@SmartEnergy.Resources.ComumTexts.lengthMenu",
                    "zeroRecords": "@SmartEnergy.Resources.ComumTexts.zeroRecords",
                    "info": "@SmartEnergy.Resources.ComumTexts.info",
                    "infoEmpty": "@SmartEnergy.Resources.ComumTexts.infoEmpty",
                    "infoFiltered": "@SmartEnergy.Resources.ComumTexts.infoFiltered"
                }

            });

            //
            // BUSCA
            //

            // campos de busca em cada coluna na tabela
            $('.dataTables-gateways tfoot th').each(function () {
                var title = $(this).text();
                if (title.length > 0)
                    $(this).html('<input type="text" />');
            });

            // dataTable
            var table = $('.dataTables-gateways').DataTable();

            // aplica a busca
            table.columns().every(function () {
                var that = this;

                $('input', this.footer()).on('keyup change', function () {
                    if (that.search() !== this.value) {
                        that
                            .search(this.value)
                            .draw();
                    }
                });
            });

            // lista somente gateways em atraso
            regExSearch = "^(?=.*?(1|2|3|4)).*?";
            $('.dataTables-gateways').DataTable().column(0).search(regExSearch, true, false).draw();

            $(".bottom").tooltip({
                placement: "bottom"
            });

            piscando();
        });

        function SelecionouTipo() {

            // pega tipo selecionado
            var tipo = document.getElementById("Tipos").selectedIndex;

            // verifica se todos
            if (tipo == 0) {
                // lista somente gateways em atraso
                regExSearch = "^(?=.*?(1|2|3|4)).*?";
                $('.dataTables-gateways').DataTable().column(0).search(regExSearch, true, false).draw();
            }
            else {
                // lista tipo selecionado
                $('.dataTables-gateways').DataTable().column(0).search(tipo).draw();
            }
        }

        function piscando() {

            var tempo = 500; //1000 = 1s

            blinks = document.getElementsByTagName("blink");
            for (var i = 0; i < blinks.length; i++) {
                if (blinks[i].getAttribute("style") == "VISIBILITY: hidden") {
                    blinks[i].setAttribute("style", "VISIBILITY: visible");
                } else {
                    blinks[i].setAttribute("style", "VISIBILITY: hidden");
                }
            }
            setTimeout('piscando()', tempo);
        }

        function ObservacoesGateway(e, IDGateway) {

            e.stopPropagation();

            // apresenta modal
            $("#ModalObservacoes").modal("show");

            // aguarde
            $('#Observacoes_resultado').css("display", "none");

            // IDGateway
            document.getElementById("IDGatewaySelecionado").value = IDGateway;

            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/_ObservacoesGateway',
                dataType: 'html',
                data: { "IDGateway": IDGateway },
                cache: false,
                async: true,
                success: function (data) {

                    $('#Observacoes_resultado').css("display", "block");
                    $('#Observacoes_resultado').html(data);
                }
            });
        }

        function FecharObservacoesGateway() {

            // fecha modal
            $("#ModalObservacoes").modal("hide");

            // atualiza pagina
            location.reload();
        }

        function ObservacaoGateway(e, IDGateway, IDObservacao) {

            e.stopPropagation();

            // apresenta modal
            $("#ModalObservacao").modal("show");

            // aguarde
            $('#Observacao_resultado').css("display", "none");

            // IDGateway e IDObservacao
            if (IDGateway == 0) {
                // IDGateway
                IDGateway = document.getElementById("IDGatewaySelecionado").value;
            }
            else {
                document.getElementById("IDGatewaySelecionado").value = IDGateway;
            }

            document.getElementById("IDObservacaoSelecionado").value = IDObservacao;

            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/_ObservacaoGateway',
                dataType: 'html',
                data: { "IDGateway": IDGateway, "IDObservacao": IDObservacao },
                cache: false,
                async: true,
                success: function (data) {

                    $('#Observacao_resultado').css("display", "block");
                    $('#Observacao_resultado').html(data);
                }
            });
        }


        function SalvarObservacaoGateway() {

            // IDObservacao
            var IDObservacao = document.getElementById("IDObservacaoSelecionado").value;

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // IDTag
            var IDTag = $("#tags").val();

            // data
            var data_relat = document.querySelector('[name="data_relat"]').value;

            // cliente visualiza
            var ClienteVisualizaObj = document.getElementsByName('ClienteVisualiza');
            var ClienteVisualiza = ClienteVisualizaObj[0].checked;

            // Observacao
            var ObservacaoTexto = document.getElementById("ObservacaoTexto").value;

            // salvar
            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/ObservacaoGateway_Salvar',
                data: { 'IDObservacao': IDObservacao, 'IDGateway': IDGateway, 'IDTag': IDTag, 'ClienteVisualiza': ClienteVisualiza, 'ObservacaoData': data_relat, 'ObservacaoTexto': ObservacaoTexto },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Suporte/_ObservacoesGateway',
                            dataType: 'html',
                            data: { "IDGateway": IDGateway },
                            cache: false,
                            async: true,
                            success: function (data) {

                                $('#Observacoes_resultado').css("display", "block");
                                $('#Observacoes_resultado').html(data);
                            }
                        });

                    }, 100);
                },
                error: function (response) {

                }
            });
        }

        function ResolvidoObservacaoGateway(IDObservacao) {
            event.stopPropagation();

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // resolvido
            $.ajax(
            {
                type: 'GET',
                url: '/Suporte/ObservacaoGateway_Resolvido',
                data: { 'IDObservacao': IDObservacao },
                contentType: 'application/html',
                dataType: 'html',
                cache: false,
                async: true,
                success: function (data) {

                    setTimeout(function () {

                        $.ajax(
                        {
                            type: 'GET',
                            url: '/Suporte/_ObservacoesGateway',
                            dataType: 'html',
                            data: { "IDGateway": IDGateway },
                            cache: false,
                            async: true,
                            success: function (data) {

                                $('#Observacoes_resultado').css("display", "block");
                                $('#Observacoes_resultado').html(data);
                            }
                        });

                    }, 100);
                },
                error: function (response) {

                }
            });
        };

        function ExcluirObservacaoGateway(IDObservacao, DataTexto) {
            event.stopPropagation();

            // IDGateway
            var IDGateway = document.getElementById("IDGatewaySelecionado").value;

            // titulo
            titulo = "Deseja excluir a Observação?<br/>" + DataTexto;

            swal({
                html: true,
                title: titulo,
                text: "Esta operação não poderá ser desfeita.",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#18a689",
                confirmButtonText: "Excluir",
                cancelButtonText: "Cancelar",
                closeOnConfirm: true
            }, function () {

                // excluir
                $.ajax(
                {
                    type: 'GET',
                    url: '/Suporte/ObservacaoGateway_Excluir',
                    data: { 'IDObservacao': IDObservacao },
                    contentType: 'application/html',
                    dataType: 'html',
                    cache: false,
                    async: true,
                    success: function (data) {

                        setTimeout(function () {

                            $.ajax(
                            {
                                type: 'GET',
                                url: '/Suporte/_ObservacoesGateway',
                                dataType: 'html',
                                data: { "IDGateway": IDGateway },
                                cache: false,
                                async: true,
                                success: function (data) {

                                    $('#Observacoes_resultado').css("display", "block");
                                    $('#Observacoes_resultado').html(data);
                                }
                            });

                        }, 100);
                    },
                    error: function (response) {

                    }
                });
            });
        };

</script>
}

