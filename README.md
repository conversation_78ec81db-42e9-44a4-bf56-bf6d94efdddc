# SmartEnergy

Sistema de gestão de energia inteligente desenvolvido em ASP.NET MVC.

## 📋 Visão Geral

O SmartEnergy é uma aplicação web para monitoramento e gestão de consumo de energia, desenvolvida com:

- **Framework**: ASP.NET MVC 5
- **Plataforma**: .NET Framework 4.6.2
- **Banco de Dados**: SQL Server
- **ORM**: Entity Framework 6.1.3
- **Cache**: Redis
- **Autenticação**: ASP.NET Identity

## 🚀 Início Rápido com Docker

A maneira mais fácil de executar o projeto é usando Docker:

### Pré-requisitos
- Docker 20.10+
- Docker Compose 2.0+
- 4GB RAM disponível

### Executar o projeto

```bash
# 1. Inicializar ambiente (primeira vez)
./docker.sh init

# 2. Iniciar aplicação
./docker.sh start

# 3. Acessar aplicação
# http://localhost:8080
```

### Comandos úteis

```bash
# Ver status dos containers
./docker.sh status

# Ver logs em tempo real
./docker.sh logs

# Parar ambiente
./docker.sh stop

# Acessar shell da aplicação
./docker.sh shell

# Fazer backup do banco
./docker.sh backup

# Ver todos os comandos
./docker.sh help
```

## 🏗️ Arquitetura Docker

O ambiente Docker inclui:

- **SmartEnergy App**: Aplicação principal (porta 8080)
- **SQL Server**: Banco de dados (porta 1433)
- **Redis**: Cache em memória (porta 6379)
- **Nginx**: Proxy reverso (porta 80)

## 📁 Estrutura do Projeto

```
smartenergy/
├── SmartEnergy/              # Código fonte da aplicação
│   ├── Controllers/          # Controllers MVC
│   ├── Models/              # Modelos de dados
│   ├── Views/               # Views Razor
│   ├── Scripts/             # JavaScript
│   ├── Content/             # CSS e assets
│   └── Web.config           # Configuração da aplicação
├── docker/                  # Configurações Docker
│   ├── docker-compose.linux.yml
│   ├── docker-compose.yml
│   ├── Dockerfile.linux
│   ├── nginx/
│   └── sql-scripts/
├── docker.sh               # Script principal Docker
└── README.md               # Este arquivo
```

## 🔧 Desenvolvimento Local

### Sem Docker

Para executar sem Docker, você precisará:

1. **Visual Studio 2017+** ou **Visual Studio Code**
2. **.NET Framework 4.6.2**
3. **SQL Server** (LocalDB ou Express)
4. **IIS Express**

```bash
# 1. Restaurar pacotes NuGet
nuget restore SmartEnergy.sln

# 2. Compilar projeto
msbuild SmartEnergy.sln /p:Configuration=Release

# 3. Executar no IIS Express
# (ou abrir no Visual Studio e pressionar F5)
```

### Com Docker (Desenvolvimento)

```bash
# Usar override para desenvolvimento
docker-compose -f docker/docker-compose.linux.yml -f docker/docker-compose.override.yml up -d

# Ou usar o script de desenvolvimento
./dev.sh start
```

## 🗄️ Banco de Dados

### Connection String

Para Docker:
```
Server=sqlserver,1433;Database=SmartEnergyDB;User Id=sa;Password=SmartEnergy123!;TrustServerCertificate=true;
```

Para desenvolvimento local:
```
Data Source=(LocalDB)\MSSQLLocalDB;AttachDbFilename=|DataDirectory|\SmartEnergyDB.mdf;Integrated Security=True
```

### Migrações

O banco é inicializado automaticamente no Docker. Para desenvolvimento local:

```bash
# No Package Manager Console do Visual Studio
Update-Database
```

## 🔐 Configuração

### Variáveis de Ambiente

Principais configurações no arquivo `.env`:

```bash
# Banco de dados
DB_PASSWORD=SmartEnergy123!
DB_NAME=SmartEnergyDB

# Email SMTP
SMTP_SERVER=email-smtp.sa-east-1.amazonaws.com
SMTP_USER=seu-usuario
SMTP_PASSWORD=sua-senha

# reCAPTCHA
RECAPTCHA_PUBLIC_KEY=sua-chave-publica
RECAPTCHA_PRIVATE_KEY=sua-chave-privada
```

### Arquivos de Configuração

- `SmartEnergy/Web.config` - Configuração principal
- `SmartEnergy/Web.Docker.config` - Configuração para Docker
- `docker/.env` - Variáveis de ambiente Docker

## 🧪 Testes

```bash
# Executar testes unitários
dotnet test

# Com Docker
./docker.sh shell
# Dentro do container:
mono --version
```

## 📦 Deploy

### Produção com Docker

1. **Alterar senhas padrão**
2. **Configurar SSL/HTTPS**
3. **Configurar backup automático**
4. **Monitoramento e logs**

```bash
# Build para produção
docker-compose -f docker/docker-compose.linux.yml build --no-cache

# Deploy
docker-compose -f docker/docker-compose.linux.yml up -d
```

### Deploy Tradicional (IIS)

1. **Publicar aplicação**:
   ```bash
   msbuild SmartEnergy.sln /p:Configuration=Release /p:PublishProfile=FolderProfile
   ```

2. **Configurar IIS**
3. **Configurar SQL Server**
4. **Configurar SSL**

## 🐛 Troubleshooting

### Problemas Comuns

**Container não inicia**:
```bash
./docker.sh logs
docker system prune -f
```

**Erro de conexão com banco**:
```bash
./docker.sh db
# Verificar se SQL Server está rodando
```

**Aplicação não compila**:
```bash
./docker.sh shell
# Verificar dependências Mono
mono --version
```

### Logs

```bash
# Logs da aplicação
./docker.sh logs

# Logs específicos
docker logs smartenergy-app
docker logs smartenergy-sqlserver
```

## 📚 Documentação

- [Documentação Docker](docker/README-Docker.md) - Guia completo do ambiente Docker
- [API Documentation](docs/api.md) - Documentação da API (se aplicável)
- [Deployment Guide](docs/deployment.md) - Guia de deploy

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está licenciado sob a [MIT License](LICENSE).

## 📞 Suporte

Para suporte e dúvidas:

- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/seu-usuario/smartenergy/issues)
- **Documentação**: [Wiki do Projeto](https://github.com/seu-usuario/smartenergy/wiki)

---

**Desenvolvido com ❤️ pela equipe SmartEnergy**
