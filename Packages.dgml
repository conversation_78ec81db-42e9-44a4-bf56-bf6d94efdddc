﻿<?xml version="1.0" encoding="utf-8"?>
<DirectedGraph GraphDirection="LeftToRight" xmlns="http://schemas.microsoft.com/vs/2009/dgml">
  <Nodes>
    <Node Id="SmartEnergy" Label="SmartEnergy" Category="Project" />
    <Node Id="Antlr 3.4.1.9004" Label="Antlr 3.4.1.9004" Category="Package" />
    <Node Id="bootstrap 3.3.0" Label="bootstrap 3.3.0" Category="Package" />
    <Node Id="Bootstrap.Datepicker 1.6.4" Label="Bootstrap.Datepicker 1.6.4" Category="Package" />
    <Node Id="Bootstrap.Datepicker.Globalize 1.0.0" Label="Bootstrap.Datepicker.Globalize 1.0.0"
      Category="Package" />
    <Node Id="bootstrap.less 3.3.5" Label="bootstrap.less 3.3.5" Category="Package" />
    <Node Id="Bootstrap.v3.Datetimepicker *********" Label="Bootstrap.v3.Datetimepicker *********"
      Category="Package" />
    <Node Id="Bootstrap.v3.Datetimepicker.CSS 4.15.35"
      Label="Bootstrap.v3.Datetimepicker.CSS 4.15.35" Category="Package" />
    <Node Id="EntityFramework 6.1.1" Label="EntityFramework 6.1.1" Category="Package" />
    <Node Id="jQuery 1.9.1" Label="jQuery 1.9.1" Category="Package" />
    <Node Id="jQuery.FileUpload 9.12.3" Label="jQuery.FileUpload 9.12.3" Category="Package" />
    <Node Id="jQuery.UI.Combined 1.9.0" Label="jQuery.UI.Combined 1.9.0" Category="Package" />
    <Node Id="jquery-globalize 0.1.1" Label="jquery-globalize 0.1.1" Category="Package" />
    <Node Id="Microsoft.AspNet.Identity.Core 2.1.0" Label="Microsoft.AspNet.Identity.Core 2.1.0"
      Category="Package" />
    <Node Id="Microsoft.AspNet.Identity.EntityFramework 2.1.0"
      Label="Microsoft.AspNet.Identity.EntityFramework 2.1.0" Category="Package" />
    <Node Id="Microsoft.AspNet.Identity.Owin 2.1.0" Label="Microsoft.AspNet.Identity.Owin 2.1.0"
      Category="Package" />
    <Node Id="Microsoft.AspNet.Mvc 5.0.0" Label="Microsoft.AspNet.Mvc 5.0.0" Category="Package" />
    <Node Id="Microsoft.AspNet.Razor 3.0.0" Label="Microsoft.AspNet.Razor 3.0.0" Category="Package" />
    <Node Id="Microsoft.AspNet.Web.Optimization 1.1.1"
      Label="Microsoft.AspNet.Web.Optimization 1.1.1" Category="Package" />
    <Node Id="Microsoft.AspNet.WebPages 3.0.0" Label="Microsoft.AspNet.WebPages 3.0.0"
      Category="Package" />
    <Node Id="Microsoft.jQuery.Unobtrusive.Validation 3.0.0"
      Label="Microsoft.jQuery.Unobtrusive.Validation 3.0.0" Category="Package" />
    <Node Id="Microsoft.Owin 3.0.0" Label="Microsoft.Owin 3.0.0" Category="Package" />
    <Node Id="Microsoft.Owin.Host.SystemWeb 2.0.0" Label="Microsoft.Owin.Host.SystemWeb 2.0.0"
      Category="Package" />
    <Node Id="Microsoft.Owin.Security 3.0.0" Label="Microsoft.Owin.Security 3.0.0"
      Category="Package" />
    <Node Id="Microsoft.Owin.Security.Cookies 2.1.0" Label="Microsoft.Owin.Security.Cookies 2.1.0"
      Category="Package" />
    <Node Id="Microsoft.Owin.Security.Facebook 2.0.0" Label="Microsoft.Owin.Security.Facebook 2.0.0"
      Category="Package" />
    <Node Id="Microsoft.Owin.Security.Google 2.0.0" Label="Microsoft.Owin.Security.Google 2.0.0"
      Category="Package" />
    <Node Id="Microsoft.Owin.Security.MicrosoftAccount 2.0.0"
      Label="Microsoft.Owin.Security.MicrosoftAccount 2.0.0" Category="Package" />
    <Node Id="Microsoft.Owin.Security.OAuth 2.1.0" Label="Microsoft.Owin.Security.OAuth 2.1.0"
      Category="Package" />
    <Node Id="Microsoft.Owin.Security.Twitter 2.0.0" Label="Microsoft.Owin.Security.Twitter 2.0.0"
      Category="Package" />
    <Node Id="Microsoft.Web.Infrastructure *******" Label="Microsoft.Web.Infrastructure *******"
      Category="Package" />
    <Node Id="Moment.js 2.9.0" Label="Moment.js 2.9.0" Category="Package" />
    <Node Id="Newtonsoft.Json 6.0.6" Label="Newtonsoft.Json 6.0.6" Category="Package" />
    <Node Id="NPOI 2.2.1" Label="NPOI 2.2.1" Category="Package" />
    <Node Id="Owin 1.0" Label="Owin 1.0" Category="Package" />
    <Node Id="Rotativa 1.6.4" Label="Rotativa 1.6.4" Category="Package" />
    <Node Id="SharpZipLib 0.86.0" Label="SharpZipLib 0.86.0" Category="Package" />
    <Node Id="WebGrease 1.5.2" Label="WebGrease 1.5.2" Category="Package" />
  </Nodes>
  <Links>
    <Link Source="bootstrap 3.3.0" Target="jQuery 1.9.1" Category="Package Dependency" />
    <Link Source="Bootstrap.Datepicker.Globalize 1.0.0" Target="Bootstrap.Datepicker 1.6.4"
      Category="Package Dependency" />
    <Link Source="Bootstrap.Datepicker.Globalize 1.0.0" Target="jquery-globalize 0.1.1"
      Category="Package Dependency" />
    <Link Source="bootstrap.less 3.3.5" Target="jQuery 1.9.1" Category="Package Dependency" />
    <Link Source="Bootstrap.v3.Datetimepicker *********" Target="bootstrap.less 3.3.5"
      Category="Package Dependency" />
    <Link Source="Bootstrap.v3.Datetimepicker *********" Target="Moment.js 2.9.0"
      Category="Package Dependency" />
    <Link Source="Bootstrap.v3.Datetimepicker.CSS 4.15.35" Target="bootstrap 3.3.0"
      Category="Package Dependency" />
    <Link Source="Bootstrap.v3.Datetimepicker.CSS 4.15.35" Target="Moment.js 2.9.0"
      Category="Package Dependency" />
    <Link Source="jQuery.FileUpload 9.12.3" Target="jQuery 1.9.1" Category="Package Dependency" />
    <Link Source="jQuery.FileUpload 9.12.3" Target="jQuery.UI.Combined 1.9.0"
      Category="Package Dependency" />
    <Link Source="jQuery.UI.Combined 1.9.0" Target="jQuery 1.9.1" Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Identity.EntityFramework 2.1.0"
      Target="Microsoft.AspNet.Identity.Core 2.1.0" Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Identity.EntityFramework 2.1.0" Target="EntityFramework 6.1.1"
      Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Identity.Owin 2.1.0"
      Target="Microsoft.AspNet.Identity.Core 2.1.0" Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Identity.Owin 2.1.0" Target="Microsoft.Owin.Security 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Identity.Owin 2.1.0"
      Target="Microsoft.Owin.Security.Cookies 2.1.0" Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Identity.Owin 2.1.0" Target="Microsoft.Owin.Security.OAuth 2.1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Mvc 5.0.0" Target="Microsoft.AspNet.WebPages 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Mvc 5.0.0" Target="Microsoft.AspNet.Razor 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Web.Optimization 1.1.1"
      Target="Microsoft.Web.Infrastructure *******" Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.Web.Optimization 1.1.1" Target="WebGrease 1.5.2"
      Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.WebPages 3.0.0" Target="Microsoft.Web.Infrastructure *******"
      Category="Package Dependency" />
    <Link Source="Microsoft.AspNet.WebPages 3.0.0" Target="Microsoft.AspNet.Razor 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin 3.0.0" Target="Owin 1.0" Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Host.SystemWeb 2.0.0" Target="Owin 1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Host.SystemWeb 2.0.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Host.SystemWeb 2.0.0" Target="Microsoft.Web.Infrastructure *******"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Host.SystemWeb 2.0.0" Target="Owin 1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Host.SystemWeb 2.0.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security 3.0.0" Target="Owin 1.0" Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security 3.0.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Cookies 2.1.0" Target="Owin 1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Cookies 2.1.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Cookies 2.1.0" Target="Microsoft.Owin.Security 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Facebook 2.0.0" Target="Owin 1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Facebook 2.0.0" Target="Newtonsoft.Json 6.0.6"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Facebook 2.0.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Facebook 2.0.0" Target="Microsoft.Owin.Security 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Google 2.0.0" Target="Owin 1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Google 2.0.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Google 2.0.0" Target="Microsoft.Owin.Security 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.MicrosoftAccount 2.0.0" Target="Owin 1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.MicrosoftAccount 2.0.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.MicrosoftAccount 2.0.0" Target="Newtonsoft.Json 6.0.6"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.MicrosoftAccount 2.0.0"
      Target="Microsoft.Owin.Security 3.0.0" Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.OAuth 2.1.0" Target="Owin 1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.OAuth 2.1.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.OAuth 2.1.0" Target="Newtonsoft.Json 6.0.6"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.OAuth 2.1.0" Target="Microsoft.Owin.Security 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Twitter 2.0.0" Target="Owin 1.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Twitter 2.0.0" Target="Microsoft.Owin 3.0.0"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Twitter 2.0.0" Target="Newtonsoft.Json 6.0.6"
      Category="Package Dependency" />
    <Link Source="Microsoft.Owin.Security.Twitter 2.0.0" Target="Microsoft.Owin.Security 3.0.0"
      Category="Package Dependency" />
    <Link Source="NPOI 2.2.1" Target="SharpZipLib 0.86.0" Category="Package Dependency" />
    <Link Source="WebGrease 1.5.2" Target="Antlr 3.4.1.9004" Category="Package Dependency" />
    <Link Source="WebGrease 1.5.2" Target="Newtonsoft.Json 6.0.6" Category="Package Dependency" />
    <Link Source="SmartEnergy" Target="Bootstrap.Datepicker.Globalize 1.0.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Bootstrap.v3.Datetimepicker *********"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Bootstrap.v3.Datetimepicker.CSS 4.15.35"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="jQuery.FileUpload 9.12.3" Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.AspNet.Identity.EntityFramework 2.1.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.AspNet.Identity.Owin 2.1.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.AspNet.Mvc 5.0.0" Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.AspNet.Web.Optimization 1.1.1"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.jQuery.Unobtrusive.Validation 3.0.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.Owin.Host.SystemWeb 2.0.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.Owin.Security.Facebook 2.0.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.Owin.Security.Google 2.0.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.Owin.Security.MicrosoftAccount 2.0.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Microsoft.Owin.Security.Twitter 2.0.0"
      Category="Installed Package" />
    <Link Source="SmartEnergy" Target="NPOI 2.2.1" Category="Installed Package" />
    <Link Source="SmartEnergy" Target="Rotativa 1.6.4" Category="Installed Package" />
  </Links>
  <Categories>
    <Category Id="Project" />
    <Category Id="Package" />
  </Categories>
  <Styles>
    <Style TargetType="Node" GroupLabel="Project" ValueLabel="True">
      <Condition Expression="HasCategory('Project')" />
      <Setter Property="Background" Value="Blue" />
    </Style>
    <Style TargetType="Link" GroupLabel="Package Dependency" ValueLabel="True">
      <Condition Expression="HasCategory('Package Dependency')" />
      <Setter Property="Background" Value="Yellow" />
    </Style>
  </Styles>
</DirectedGraph>